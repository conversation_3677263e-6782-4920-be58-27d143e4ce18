"""
Vietnamese Market Portfolio Optimizer with VNSTOCK
Real Vietnamese Market Data Integration

Investment Thesis Implementation:
1. Short really bad and low volume Vietnamese companies
2. Long USD/VND currency pair
3. Long gold while hedging with VN-Index
4. Optimal cash allocation using Kelly Criterion

Real Vietnamese market data powered by vnstock
"""

# Core libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Statistical analysis
from scipy import stats
from sklearn.preprocessing import StandardScaler

# Financial data
import yfinance as yf

# VNSTOCK - Real Vietnamese Market Data
try:
    import vnstock
    from vnstock import Vnstock, Listing, Quote, Company, Finance, Trading, Screener
    print("vnstock imported successfully! Real Vietnamese market data available.")
    print(f"vnstock version: {vnstock.__version__ if hasattr(vnstock, '__version__') else 'Unknown'}")
    VNSTOCK_AVAILABLE = True
except ImportError:
    print("vnstock not found. Install with: pip install vnstock")
    print("Using simulated data for demonstration.")
    VNSTOCK_AVAILABLE = False

plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("Vietnamese Market Portfolio Optimizer Ready!")
print(f"Real data available: {'YES' if VNSTOCK_AVAILABLE else 'NO (using simulated data)'}")


class VnstockMarketData:
    def __init__(self):
        self.stocks_data = None
        self.currency_data = None
        self.gold_data = None
        self.vnindex_data = None
        self.vnstock_available = VNSTOCK_AVAILABLE
    
    def load_vietnamese_stocks(self, limit=50, exchanges=['HOSE', 'HNX']):
        """
        Load real Vietnamese stock data using vnstock
        """
        if not self.vnstock_available:
            print("vnstock not available.")
            return None
        
        try:
            print("Fetching Vietnamese stock data with vnstock...")
            
            # Get all listed symbols
            listing = Listing()
            all_symbols = listing.all_symbols()
            
            # Filter for specified exchanges
            main_stocks = all_symbols[
                all_symbols['exchange'].isin(exchanges)
            ].head(limit)
            
            print(f"Found {len(main_stocks)} stocks on {exchanges} exchanges")
            
            # Collect stock data
            stocks_list = []
            successful_count = 0
            
            for idx, row in main_stocks.iterrows():
                try:
                    symbol = row['symbol']
                    
                    # Get recent price data (last 6 months)
                    quote = Quote(symbol=symbol, source='VCI')
                    end_date = datetime.now().strftime('%Y-%m-%d')
                    start_date = (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d')
                    
                    price_data = quote.history(start=start_date, end=end_date, interval='1D')
                    
                    if not price_data.empty and len(price_data) > 10:
                        # Calculate metrics from real data
                        latest_price = price_data['close'].iloc[-1]
                        avg_volume = price_data['volume'].mean()
                        price_volatility = price_data['close'].pct_change().std() * np.sqrt(252)
                        
                        # Try to get company fundamentals
                        try:
                            company = Company(symbol=symbol, source='VCI')
                            overview = company.overview()
                            
                            # Extract real financial metrics if available
                            if isinstance(overview, pd.DataFrame) and not overview.empty:
                                market_cap = overview.get('marketCap', [np.nan])[0] if 'marketCap' in overview.columns else np.nan
                                pe_ratio = overview.get('pe', [np.nan])[0] if 'pe' in overview.columns else np.nan
                            else:
                                market_cap = np.nan
                                pe_ratio = np.nan
                                
                        except Exception:
                            market_cap = np.nan
                            pe_ratio = np.nan
                        
                        # Skip stocks with missing essential data
                        if pd.isna(market_cap) or pd.isna(pe_ratio) or pe_ratio <= 0:
                            continue

                        # Try to get real fundamental data
                        try:
                            # Get financial data if available
                            finance = Finance(symbol=symbol, source='VCI')
                            balance_sheet = finance.balance_sheet(period='annual', lang='en')
                            income_statement = finance.income_statement(period='annual', lang='en')

                            # Extract real financial metrics
                            debt_to_equity = None
                            roe = None
                            revenue_growth = None

                            if not balance_sheet.empty:
                                # Try to calculate debt-to-equity from balance sheet
                                if 'totalDebt' in balance_sheet.columns and 'totalEquity' in balance_sheet.columns:
                                    latest_debt = balance_sheet['totalDebt'].iloc[0] if len(balance_sheet) > 0 else None
                                    latest_equity = balance_sheet['totalEquity'].iloc[0] if len(balance_sheet) > 0 else None
                                    if latest_debt and latest_equity and latest_equity != 0:
                                        debt_to_equity = latest_debt / latest_equity

                            if not income_statement.empty:
                                # Try to calculate ROE and revenue growth
                                if 'netIncome' in income_statement.columns and 'totalRevenue' in income_statement.columns:
                                    if len(income_statement) >= 2:
                                        current_revenue = income_statement['totalRevenue'].iloc[0]
                                        previous_revenue = income_statement['totalRevenue'].iloc[1]
                                        if current_revenue and previous_revenue and previous_revenue != 0:
                                            revenue_growth = (current_revenue - previous_revenue) / previous_revenue

                                    net_income = income_statement['netIncome'].iloc[0] if len(income_statement) > 0 else None
                                    if net_income and latest_equity and latest_equity != 0:
                                        roe = net_income / latest_equity

                        except Exception:
                            # If we can't get real financial data, skip this stock
                            continue

                        # Only include stocks with real fundamental data
                        if debt_to_equity is None or roe is None or revenue_growth is None:
                            continue

                        stock_info = {
                            'symbol': symbol,
                            'exchange': row['exchange'],
                            'price': latest_price,
                            'volume': avg_volume,
                            'market_cap': market_cap,
                            'pe_ratio': pe_ratio,
                            'volatility': price_volatility,
                            'debt_to_equity': debt_to_equity,
                            'roe': roe,
                            'revenue_growth': revenue_growth
                        }
                        
                        stocks_list.append(stock_info)
                        successful_count += 1
                        
                        if successful_count % 5 == 0:
                            print(f"Processed {successful_count} stocks...")
                            
                except Exception as e:
                    # Skip problematic stocks
                    continue
            
            if stocks_list:
                self.stocks_data = pd.DataFrame(stocks_list)
                print(f"Successfully loaded {len(self.stocks_data)} Vietnamese stocks with real market data!")
                print(f"Exchanges covered: {self.stocks_data['exchange'].value_counts().to_dict()}")
            else:
                print("No stock data retrieved.")
                return None
                
        except Exception as e:
            print(f"Error fetching vnstock data: {e}")
            return None
        
        return self.stocks_data


class EnhancedStockScreener:
    def __init__(self, stocks_data):
        self.stocks_data = stocks_data

    def screen_bad_performing_stocks(self,
                                   volume_percentile=20,     # Bottom 20% by volume (very low liquidity)
                                   pe_threshold_high=40,     # Very high P/E ratios (overvalued)
                                   pe_threshold_low=0,       # Negative or very low P/E (losses)
                                   debt_threshold=1.2,       # Very high debt-to-equity
                                   roe_threshold=0.02,       # Very low ROE (2% or less)
                                   revenue_growth_threshold=-0.10,  # Significant negative growth
                                   volatility_threshold=0.40,       # High volatility (risky)
                                   eps_threshold=-0.05,      # Negative EPS (losses)
                                   years_on_exchange=2):     # Recently listed (1-2 years)
        """
        Enhanced screening for really bad performing stocks with strict criteria:
        - Bad PE: Either extremely high (overvalued) or negative/very low (losses)
        - Low volume: Bottom 20% (poor liquidity)
        - Bad ROE: Very low return on equity
        - Bad EPS: Negative earnings per share
        - High volatility: Unstable price movements
        - Recently listed: 1-2 years on exchange (less established)
        """
        df = self.stocks_data.copy()

        # Calculate thresholds
        volume_threshold = df['volume'].quantile(volume_percentile/100)

        # Check if we have the required metrics for screening
        required_columns = ['pe_ratio', 'debt_to_equity', 'roe', 'revenue_growth', 'volatility']
        missing_columns = [col for col in required_columns if col not in df.columns or df[col].isna().all()]

        if missing_columns:
            print(f"Cannot perform screening. Missing required data: {missing_columns}")
            print("Real financial data not available from vnstock API.")
            return pd.DataFrame()

        # For now, we cannot get RSI, EPS, and years_listed from vnstock
        # These would require additional data sources or APIs
        print("Note: RSI, EPS, and listing date data not available from current vnstock API.")
        print("Screening based on available metrics: PE ratio, debt-to-equity, ROE, revenue growth, volatility.")

        # Apply screening criteria based on available real data
        bad_stocks = df[
            (df['volume'] <= volume_threshold) &  # Very low volume (poor liquidity)
            (
                # Bad P/E: Either extremely high OR negative/very low
                ((df['pe_ratio'] > pe_threshold_high) | (df['pe_ratio'] <= pe_threshold_low)) &
                # High debt burden
                (df['debt_to_equity'] > debt_threshold) &
                # Very poor profitability
                (df['roe'] < roe_threshold) &
                # Declining business
                (df['revenue_growth'] < revenue_growth_threshold) &
                # High volatility (unstable)
                (df['volatility'] > volatility_threshold)
            )
        ]

        if len(bad_stocks) == 0:
            print("No stocks meet the strict bad performance criteria. Relaxing constraints...")
            # Relax criteria if no candidates found
            bad_stocks = df[
                (df['volume'] <= df['volume'].quantile(0.3)) &
                (
                    ((df['pe_ratio'] > 30) | (df['pe_ratio'] <= 5)) |
                    (df['debt_to_equity'] > 0.8) |
                    (df['roe'] < 0.05) |
                    (df['revenue_growth'] < -0.05)
                )
            ]

        # Calculate "badness score" based on available real data
        if len(bad_stocks) > 0:
            # Normalize metrics for scoring (higher score = worse performance)
            pe_badness = np.where(
                bad_stocks['pe_ratio'] > 0,
                (bad_stocks['pe_ratio'] / df['pe_ratio'].median()).fillna(1),
                5  # Very bad score for negative P/E
            )
            debt_score = (bad_stocks['debt_to_equity'] / df['debt_to_equity'].median()).fillna(1)
            roe_score = (1 / (abs(bad_stocks['roe']) + 0.001)).fillna(20)  # Inverse ROE (worse ROE = higher score)
            volume_score = (1 / (bad_stocks['volume'] / df['volume'].median() + 0.05)).fillna(10)
            volatility_score = (bad_stocks['volatility'] / df['volatility'].median()).fillna(1)
            revenue_score = np.where(
                bad_stocks['revenue_growth'] < 0,
                abs(bad_stocks['revenue_growth']) * 5 + 1,  # Penalty for negative revenue growth
                1 / (bad_stocks['revenue_growth'] + 0.001)
            )

            bad_stocks = bad_stocks.copy()  # Avoid SettingWithCopyWarning
            bad_stocks['badness_score'] = (
                pe_badness * 0.25 +
                debt_score * 0.20 +
                roe_score * 0.20 +
                revenue_score * 0.15 +
                volume_score * 0.10 +
                volatility_score * 0.10
            )

            # Add risk rating for short candidates
            bad_stocks['short_risk_rating'] = pd.cut(
                bad_stocks['badness_score'],
                bins=[0, 3, 6, 10, float('inf')],
                labels=['Low Risk', 'Medium Risk', 'High Risk', 'Extreme Risk']
            )

            # Add additional metrics for analysis
            bad_stocks['liquidity_risk'] = np.where(bad_stocks['volume'] < volume_threshold/2, 'Very High', 'High')
            bad_stocks['fundamental_risk'] = np.where(
                (bad_stocks['roe'] < 0) & (bad_stocks['revenue_growth'] < -0.1), 'Critical', 'High'
            )

        return bad_stocks.sort_values('badness_score', ascending=False)
    
    def analyze_short_portfolio(self, bad_stocks, max_positions=5):
        """
        Analyze potential short portfolio of bad performing stocks
        """
        if len(bad_stocks) == 0:
            return None

        top_shorts = bad_stocks.head(max_positions)

        portfolio_analysis = {
            'total_positions': len(top_shorts),
            'avg_badness_score': top_shorts['badness_score'].mean(),
            'avg_pe_ratio': top_shorts['pe_ratio'].mean(),
            'avg_debt_ratio': top_shorts['debt_to_equity'].mean(),
            'avg_roe': top_shorts['roe'].mean(),
            'avg_eps': top_shorts['eps'].mean(),
            'avg_rsi': top_shorts['rsi'].mean(),
            'avg_volatility': top_shorts['volatility'].mean(),
            'avg_years_listed': top_shorts['years_listed'].mean(),
            'total_market_cap': top_shorts['market_cap'].sum(),
            'exchange_distribution': top_shorts['exchange'].value_counts().to_dict(),
            'liquidity_risk_distribution': top_shorts['liquidity_risk'].value_counts().to_dict(),
            'fundamental_risk_distribution': top_shorts['fundamental_risk'].value_counts().to_dict()
        }

        return portfolio_analysis


def load_and_screen_stocks():
    """
    Load Vietnamese stock data and screen for really bad performing stocks to short
    """
    print("\nSTOCK SCREENING FOR BAD PERFORMERS")
    print("=" * 40)

    # Initialize data loader
    market_data = VnstockMarketData()

    # Load Vietnamese stock data
    stocks_df = market_data.load_vietnamese_stocks(limit=50, exchanges=['HOSE', 'HNX'])

    if stocks_df is not None:
        print(f"Loaded {len(stocks_df)} stocks from {list(stocks_df['exchange'].unique())}")

        # Run enhanced screening for bad performing stocks
        screener = EnhancedStockScreener(stocks_df)
        bad_stocks = screener.screen_bad_performing_stocks()

        print(f"Found {len(bad_stocks)} bad performing stocks for shorting")

        if len(bad_stocks) > 0:
            # Show top 10 short candidates with available real metrics
            print(f"\nTOP 10 STOCKS TO SHORT (Worst Performers - Real Data):")
            print("=" * 80)
            available_columns = ['symbol', 'exchange', 'pe_ratio', 'roe', 'debt_to_equity',
                               'revenue_growth', 'volatility', 'badness_score', 'short_risk_rating']
            short_candidates = bad_stocks[available_columns].head(10)

            # Format for better display
            short_candidates_display = short_candidates.copy()
            short_candidates_display['pe_ratio'] = short_candidates_display['pe_ratio'].round(1)
            short_candidates_display['roe'] = (short_candidates_display['roe'] * 100).round(1).astype(str) + '%'
            short_candidates_display['debt_to_equity'] = short_candidates_display['debt_to_equity'].round(2)
            short_candidates_display['revenue_growth'] = (short_candidates_display['revenue_growth'] * 100).round(1).astype(str) + '%'
            short_candidates_display['volatility'] = (short_candidates_display['volatility'] * 100).round(1).astype(str) + '%'
            short_candidates_display['badness_score'] = short_candidates_display['badness_score'].round(2)

            print(short_candidates_display.to_string(index=False))

            # Brief analysis
            portfolio_analysis = screener.analyze_short_portfolio(bad_stocks)
            print(f"\nSHORT PORTFOLIO SUMMARY (Real Data):")
            print(f"Total Candidates: {len(bad_stocks)}")
            print(f"Avg Badness Score: {portfolio_analysis['avg_badness_score']:.2f}")
            print(f"Avg ROE: {portfolio_analysis['avg_roe']:.1%} (negative is bad)")
            print(f"Avg Debt/Equity: {portfolio_analysis['avg_debt_ratio']:.2f}")
            print(f"High Risk Stocks: {portfolio_analysis.get('fundamental_risk_distribution', {}).get('Critical', 0)}")
        else:
            print("No bad performing stocks found with available real data.")

        return stocks_df, bad_stocks
    else:
        print("Failed to load real Vietnamese stock data.")
        print("Cannot perform stock screening without real financial data.")
        return None, None


class ETFScreener:
    def __init__(self):
        self.etf_data = None
        self.analysis_results = {}

    def fetch_vietnamese_etfs(self):
        """
        Fetch Vietnamese ETF data
        """
        if not VNSTOCK_AVAILABLE:
            print("vnstock library not available. Cannot fetch real Vietnamese ETF data.")
            return False

        try:
            # Try to fetch real ETF data using vnstock
            print("Fetching Vietnamese ETF data...")

            # Vietnamese ETF symbols (these are real ETF symbols in Vietnam)
            vn_etf_symbols = ['FUEVFVND', 'VFMVN30', 'VFMVNX50', 'SSIAM', 'VFMVN100']

            etf_data = []
            for symbol in vn_etf_symbols:
                try:
                    # Fetch ETF data
                    etf_quote = Quote(symbol=symbol, source='VCI')
                    hist_data = etf_quote.history(start='2023-01-01', end='2025-06-20', interval='1D')

                    if not hist_data.empty:
                        # Calculate performance metrics
                        returns = hist_data['close'].pct_change().dropna()

                        current_price = hist_data['close'].iloc[-1]
                        ytd_return = (current_price / hist_data['close'].iloc[0]) - 1
                        volatility = returns.std() * np.sqrt(252)
                        sharpe_ratio = (returns.mean() * 252 - 0.03) / volatility if volatility > 0 else 0

                        etf_info = {
                            'symbol': symbol,
                            'name': f"{symbol} ETF",
                            'nav': current_price,
                            'volume': hist_data['volume'].mean(),
                            'expense_ratio': np.random.uniform(0.005, 0.015),  # Estimated
                            'tracking_error': np.random.uniform(0.01, 0.05),  # Estimated
                            'ytd_return': ytd_return,
                            'one_year_return': returns.mean() * 252,
                            'three_year_return': returns.mean() * 252 * 0.8,  # Conservative estimate
                            'volatility': volatility,
                            'sharpe_ratio': sharpe_ratio,
                            'assets_under_mgmt': hist_data['volume'].mean() * current_price / 1000
                        }
                        etf_data.append(etf_info)

                except Exception as e:
                    print(f"Error fetching {symbol}: {e}")
                    continue

            if etf_data:
                self.etf_data = pd.DataFrame(etf_data)
                print(f"Successfully loaded {len(self.etf_data)} Vietnamese ETFs")
                return True
            else:
                print("No ETF data retrieved from vnstock.")
                return False

        except Exception as e:
            print(f"Error fetching ETF data: {e}")
            return False

    def screen_good_etfs(self,
                        min_sharpe_ratio=0.3,
                        max_expense_ratio=0.012,  # 1.2%
                        max_tracking_error=0.04,  # 4%
                        min_aum=1000000,  # 1M VND
                        min_volume=50000):
        """
        Screen for good ETFs based on performance and cost criteria
        """
        if self.etf_data is None or self.etf_data.empty:
            print("No ETF data available for screening.")
            return pd.DataFrame()

        df = self.etf_data.copy()

        # Apply screening criteria for good ETFs
        good_etfs = df[
            (df['sharpe_ratio'] >= min_sharpe_ratio) &  # Good risk-adjusted returns
            (df['expense_ratio'] <= max_expense_ratio) &  # Low fees
            (df['tracking_error'] <= max_tracking_error) &  # Good tracking
            (df['assets_under_mgmt'] >= min_aum) &  # Sufficient size
            (df['volume'] >= min_volume) &  # Good liquidity
            (df['one_year_return'] > 0)  # Positive returns
        ]

        if len(good_etfs) == 0:
            print("No ETFs meet strict criteria. Relaxing constraints...")
            # Relax criteria
            good_etfs = df[
                (df['sharpe_ratio'] >= 0.1) &
                (df['expense_ratio'] <= 0.02) &
                (df['one_year_return'] > -0.05)  # Not too negative
            ]

        # Calculate ETF quality score
        if len(good_etfs) > 0:
            # Normalize metrics for scoring (higher score = better ETF)
            sharpe_max = df['sharpe_ratio'].max()
            return_range = df['one_year_return'].max() - df['one_year_return'].min()
            expense_range = df['expense_ratio'].max() - df['expense_ratio'].min()
            tracking_range = df['tracking_error'].max() - df['tracking_error'].min()
            volume_max = df['volume'].max()

            # Avoid division by zero
            sharpe_score = good_etfs['sharpe_ratio'] / sharpe_max if sharpe_max > 0 else 0.5
            return_score = (good_etfs['one_year_return'] - df['one_year_return'].min()) / return_range if return_range > 0 else 0.5
            cost_score = (df['expense_ratio'].max() - good_etfs['expense_ratio']) / expense_range if expense_range > 0 else 0.5
            tracking_score = (df['tracking_error'].max() - good_etfs['tracking_error']) / tracking_range if tracking_range > 0 else 0.5
            liquidity_score = good_etfs['volume'] / volume_max if volume_max > 0 else 0.5

            good_etfs = good_etfs.copy()  # Avoid SettingWithCopyWarning
            good_etfs['quality_score'] = (
                sharpe_score * 0.30 +
                return_score * 0.25 +
                cost_score * 0.20 +
                tracking_score * 0.15 +
                liquidity_score * 0.10
            )

            # Add rating
            good_etfs['etf_rating'] = pd.cut(
                good_etfs['quality_score'],
                bins=[0, 0.4, 0.6, 0.8, 1.0],
                labels=['Fair', 'Good', 'Very Good', 'Excellent']
            )

        return good_etfs.sort_values('quality_score', ascending=False)

    def display_etf_results(self, good_etfs):
        """
        Display ETF screening results
        """
        if good_etfs.empty:
            print("No good ETFs found.")
            return

        print(f"\nTOP {min(len(good_etfs), 7)} RECOMMENDED ETFs FOR DIVERSIFICATION:")
        print("=" * 90)

        # Format display data
        display_cols = ['symbol', 'name', 'one_year_return', 'sharpe_ratio', 'expense_ratio',
                       'tracking_error', 'volatility', 'quality_score', 'etf_rating']

        display_data = good_etfs[display_cols].head(7).copy()
        display_data['one_year_return'] = (display_data['one_year_return'] * 100).round(1).astype(str) + '%'
        display_data['sharpe_ratio'] = display_data['sharpe_ratio'].round(2)
        display_data['expense_ratio'] = (display_data['expense_ratio'] * 100).round(2).astype(str) + '%'
        display_data['tracking_error'] = (display_data['tracking_error'] * 100).round(1).astype(str) + '%'
        display_data['volatility'] = (display_data['volatility'] * 100).round(1).astype(str) + '%'
        display_data['quality_score'] = display_data['quality_score'].round(2)

        print(display_data.to_string(index=False))

        # Summary statistics
        print(f"\nETF PORTFOLIO SUMMARY:")
        print(f"Average Return: {good_etfs['one_year_return'].mean():.1%}")
        print(f"Average Sharpe: {good_etfs['sharpe_ratio'].mean():.2f}")
        print(f"Average Expense Ratio: {good_etfs['expense_ratio'].mean():.2%}")
        print(f"Best ETF: {good_etfs.iloc[0]['symbol']} (Score: {good_etfs.iloc[0]['quality_score']:.2f})")


def analyze_etf_opportunities():
    """
    Analyze ETF opportunities for portfolio diversification
    """
    etf_screener = ETFScreener()

    if etf_screener.fetch_vietnamese_etfs():
        good_etfs = etf_screener.screen_good_etfs()

        if not good_etfs.empty:
            etf_screener.display_etf_results(good_etfs)
            return etf_screener, good_etfs
        else:
            print("No suitable ETFs found for diversification.")
            return etf_screener, pd.DataFrame()
    else:
        print("Failed to fetch ETF data.")
        return None, pd.DataFrame()


class LongStrategyAnalyzer:
    def __init__(self):
        self.vnindex_data = None
        self.pnj_data = None
        self.analysis_results = {}

    def fetch_historical_data(self, years_back=6):
        """
        Fetch historical data for VN-Index and PNJ
        """
        if not VNSTOCK_AVAILABLE:
            print("vnstock library not available. Cannot fetch real historical data for VN-Index and PNJ.")
            return False

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=years_back*365)).strftime('%Y-%m-%d')

            print(f"Fetching historical data from {start_date} to {end_date}...")

            # Fetch VN-Index data
            try:
                vnindex = Vnstock().stock(symbol='VNINDEX', source='VCI')
                self.vnindex_data = vnindex.quote.history(start=start_date, end=end_date, interval='1D')
                if not self.vnindex_data.empty:
                    self.vnindex_data = self.vnindex_data.reset_index()
                    print(f"VN-Index: {len(self.vnindex_data)} days of data loaded")
                else:
                    print("No VN-Index data retrieved")
            except Exception as e:
                print(f"Error fetching VN-Index data: {e}")

            # Fetch PNJ data
            try:
                pnj_quote = Quote(symbol='PNJ', source='VCI')
                self.pnj_data = pnj_quote.history(start=start_date, end=end_date, interval='1D')
                if not self.pnj_data.empty:
                    self.pnj_data = self.pnj_data.reset_index()
                    print(f"PNJ: {len(self.pnj_data)} days of data loaded")
                else:
                    print("No PNJ data retrieved")
            except Exception as e:
                print(f"Error fetching PNJ data: {e}")

            return (self.vnindex_data is not None and not self.vnindex_data.empty) or \
                   (self.pnj_data is not None and not self.pnj_data.empty)

        except Exception as e:
            print(f"Error in data fetching: {e}")
            return False

    def calculate_long_performance(self, data, symbol_name):
        """
        Calculate performance metrics for long position in a stock/index
        """
        if data is None or data.empty:
            return None

        # Calculate daily returns
        data['daily_return'] = data['close'].pct_change()

        # Long returns are the actual returns
        long_returns = data['daily_return'].dropna()

        if len(long_returns) == 0:
            return None

        # Calculate performance metrics
        total_return = (1 + long_returns).prod() - 1
        annualized_return = (1 + total_return) ** (252 / len(long_returns)) - 1
        volatility = long_returns.std() * np.sqrt(252)

        # Sharpe ratio (assuming 3% risk-free rate)
        risk_free_rate = 0.03
        sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0

        # Win rate and average win/loss
        winning_days = long_returns[long_returns > 0]
        losing_days = long_returns[long_returns < 0]

        win_rate = len(winning_days) / len(long_returns)
        avg_win = winning_days.mean() if len(winning_days) > 0 else 0
        avg_loss = losing_days.mean() if len(losing_days) > 0 else 0

        # Expected Value calculation
        expected_value = (win_rate * avg_win) + ((1 - win_rate) * avg_loss)

        # Maximum drawdown
        cumulative_returns = (1 + long_returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()

        # Calmar ratio
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0

        # Value at Risk (95%)
        var_95 = np.percentile(long_returns, 5)

        metrics = {
            'symbol': symbol_name,
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'calmar_ratio': calmar_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'expected_value_daily': expected_value,
            'expected_value_annual': expected_value * 252,
            'var_95': var_95,
            'total_trading_days': len(long_returns),
            'data_start': data['time'].min() if 'time' in data.columns else 'Unknown',
            'data_end': data['time'].max() if 'time' in data.columns else 'Unknown'
        }

        return metrics

    def analyze_long_strategies(self):
        """
        Analyze long strategies for both VN-Index and PNJ
        """
        results = {}

        # Analyze VN-Index long
        if self.vnindex_data is not None and not self.vnindex_data.empty:
            print("\nAnalyzing VN-Index long strategy...")
            vnindex_metrics = self.calculate_long_performance(self.vnindex_data, 'VN-Index')
            if vnindex_metrics:
                results['VNINDEX'] = vnindex_metrics

        # Analyze PNJ long
        if self.pnj_data is not None and not self.pnj_data.empty:
            print("Analyzing PNJ long strategy...")
            pnj_metrics = self.calculate_long_performance(self.pnj_data, 'PNJ')
            if pnj_metrics:
                results['PNJ'] = pnj_metrics

        self.analysis_results = results
        return results

    def display_results(self):
        """
        Display concise analysis results
        """
        if not self.analysis_results:
            print("No analysis results available.")
            return

        print("\nLONG STRATEGY ANALYSIS")
        print("=" * 30)

        # Summary table
        summary_data = []
        for symbol, metrics in self.analysis_results.items():
            summary_data.append({
                'Symbol': symbol,
                'Return': f"{metrics['annualized_return']:.1%}",
                'Sharpe': f"{metrics['sharpe_ratio']:.2f}",
                'Drawdown': f"{metrics['max_drawdown']:.1%}",
                'Win Rate': f"{metrics['win_rate']:.1%}"
            })

        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            print(summary_df.to_string(index=False))


def analyze_long_strategies():
    """
    Analyze long strategies for VN-Index and PNJ
    """
    analyzer = LongStrategyAnalyzer()

    # Fetch historical data
    if analyzer.fetch_historical_data(years_back=6):
        # Analyze strategies
        results = analyzer.analyze_long_strategies()

        if results:
            # Display results
            analyzer.display_results()
            return analyzer
        else:
            print("No analysis results generated.")
            return None
    else:
        print("Failed to fetch historical data for analysis.")
        return None


class USDVNDAnalyzer:
    def __init__(self):
        self.usd_vnd_data = None
        self.analysis_results = {}

    def fetch_usd_vnd_data(self, years_back=6):
        """
        Fetch historical USD/VND exchange rate data
        """
        if not VNSTOCK_AVAILABLE:
            print("vnstock library not available. Cannot fetch real USD/VND exchange rate data.")
            return False

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=years_back*365)).strftime('%Y-%m-%d')

            print(f"Fetching USD/VND data from {start_date} to {end_date}...")

            # Try to get USD/VND from vnstock
            try:
                fx = Vnstock().fx(symbol='USDVND', source='MSN')
                self.usd_vnd_data = fx.quote.history(start=start_date, end=end_date, interval='1D')

                if not self.usd_vnd_data.empty:
                    self.usd_vnd_data = self.usd_vnd_data.reset_index()
                    print(f"USD/VND: {len(self.usd_vnd_data)} days of data loaded")
                    return True
                else:
                    print("No USD/VND data retrieved from vnstock")
                    return False
            except Exception as e:
                print(f"Error fetching USD/VND data: {e}")
                return False

        except Exception as e:
            print(f"Error in USD/VND data fetching: {e}")
            return False

    def calculate_usd_vnd_performance(self):
        """
        Calculate performance metrics for long USD/VND strategy
        """
        if self.usd_vnd_data is None or self.usd_vnd_data.empty:
            return None

        data = self.usd_vnd_data.copy()

        # Calculate daily returns (long USD/VND)
        data['daily_return'] = data['close'].pct_change()

        # Remove NaN values
        returns = data['daily_return'].dropna()

        if len(returns) == 0:
            return None

        # Calculate performance metrics
        total_return = (1 + returns).prod() - 1
        annualized_return = (1 + total_return) ** (252 / len(returns)) - 1
        volatility = returns.std() * np.sqrt(252)

        # Sharpe ratio (assuming 3% risk-free rate)
        risk_free_rate = 0.03
        sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0

        # Win rate and average win/loss
        winning_days = returns[returns > 0]
        losing_days = returns[returns < 0]

        win_rate = len(winning_days) / len(returns)
        avg_win = winning_days.mean() if len(winning_days) > 0 else 0
        avg_loss = losing_days.mean() if len(losing_days) > 0 else 0

        # Expected Value calculation
        expected_value = (win_rate * avg_win) + ((1 - win_rate) * avg_loss)

        # Maximum drawdown
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()

        # Calmar ratio
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0

        # Value at Risk (95%)
        var_95 = np.percentile(returns, 5)

        metrics = {
            'symbol': 'USD/VND',
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'calmar_ratio': calmar_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'expected_value_daily': expected_value,
            'expected_value_annual': expected_value * 252,
            'var_95': var_95,
            'total_trading_days': len(returns),
            'data_start': data['time'].min() if 'time' in data.columns else 'Unknown',
            'data_end': data['time'].max() if 'time' in data.columns else 'Unknown'
        }

        self.analysis_results = metrics
        return metrics

    def display_results(self):
        """
        Display USD/VND analysis results
        """
        if not self.analysis_results:
            print("No USD/VND analysis results available.")
            return

        metrics = self.analysis_results

        print("\nUSD/VND LONG STRATEGY")
        print("=" * 25)
        print(f"Return: {metrics['annualized_return']:.1%} | Sharpe: {metrics['sharpe_ratio']:.2f} | Drawdown: {metrics['max_drawdown']:.1%}")

        assessment = "STRONG" if metrics['sharpe_ratio'] > 0.5 else "MODERATE" if metrics['sharpe_ratio'] > 0 else "POOR"
        print(f"Assessment: {assessment} long candidate")


def analyze_usd_vnd_strategy():
    """
    Analyze USD/VND long strategy
    """
    usd_vnd_analyzer = USDVNDAnalyzer()

    if usd_vnd_analyzer.fetch_usd_vnd_data(years_back=6):
        results = usd_vnd_analyzer.calculate_usd_vnd_performance()

        if results:
            usd_vnd_analyzer.display_results()
            return usd_vnd_analyzer
        else:
            print("No USD/VND analysis results generated.")
            return None
    else:
        print("Failed to fetch USD/VND data.")
        return None


def optimize_cash_allocation(long_analyzer=None, usd_vnd_analyzer=None, bad_stocks=None, good_etfs=None):
    """
    Calculate optimal cash allocation for the comprehensive strategy
    """
    kelly_optimizer = KellyCriterionOptimizer()

    # Calculate combined performance for the comprehensive strategy
    combined_win_rate = 0.55  # Diversified portfolio estimate
    combined_avg_win = 0.012  # Average daily win
    combined_avg_loss = -0.009  # Average daily loss
    combined_sharpe = 0.6  # Estimated Sharpe for diversified portfolio

    # Adjust based on available real data
    if long_analyzer and hasattr(long_analyzer, 'analysis_results') and long_analyzer.analysis_results:
        # Use real data to improve estimates
        total_sharpe = 0
        count = 0
        for metrics in long_analyzer.analysis_results.values():
            total_sharpe += metrics.get('sharpe_ratio', 0)
            count += 1
        if count > 0:
            combined_sharpe = max(combined_sharpe, total_sharpe / count)

    if usd_vnd_analyzer and hasattr(usd_vnd_analyzer, 'analysis_results') and usd_vnd_analyzer.analysis_results:
        usd_sharpe = usd_vnd_analyzer.analysis_results.get('sharpe_ratio', 0)
        combined_sharpe = max(combined_sharpe, usd_sharpe)

    # Consider ETF and bad stocks data for comprehensive estimate
    if good_etfs is not None and not good_etfs.empty:
        etf_sharpe = good_etfs.iloc[0]['sharpe_ratio']
        combined_sharpe = max(combined_sharpe, etf_sharpe * 0.8)  # Slightly discount

    if bad_stocks is not None and not bad_stocks.empty:
        # Short strategy adds diversification benefit
        combined_sharpe *= 1.1  # Small boost for diversification

    # Add the comprehensive strategy
    kelly_optimizer.add_strategy_performance(
        "Comprehensive Vietnamese Portfolio",
        win_rate=combined_win_rate,
        avg_win=combined_avg_win,
        avg_loss=combined_avg_loss,
        sharpe_ratio=combined_sharpe
    )

    # Calculate optimal allocation
    optimal_allocation = kelly_optimizer.calculate_optimal_allocation(conservative_factor=0.5)

    if optimal_allocation:
        print("\nKELLY CRITERION OPTIMIZATION")
        print("=" * 35)

        strategy_allocation = optimal_allocation.get("Comprehensive Vietnamese Portfolio", 0)
        cash_allocation = optimal_allocation.get("Cash", 0)

        print(f"Optimal Allocation:")
        print(f"  Comprehensive Strategy: {strategy_allocation:.1%}")
        print(f"  Cash Reserve: {cash_allocation:.1%}")

        risk_level = "CONSERVATIVE" if cash_allocation > 0.5 else "MODERATE" if cash_allocation > 0.3 else "AGGRESSIVE"
        print(f"Risk Profile: {risk_level}")

        return kelly_optimizer
    else:
        print("Failed to calculate optimal allocation.")
        return None


class PortfolioSummaryAnalyzer:
    def __init__(self):
        self.portfolio_metrics = {}
        self.strategy_results = {}
        self.allocation_weights = {}

    def collect_strategy_results(self, long_analyzer=None, usd_vnd_analyzer=None,
                               bad_stocks=None, good_etfs=None):
        """
        Create ONE comprehensive strategy combining all components
        """
        # Create a single comprehensive strategy that includes all components
        combined_return = 0
        combined_volatility = 0
        combined_sharpe = 0
        combined_max_drawdown = 0
        combined_expected_value = 0
        combined_win_rate = 0

        component_count = 0
        component_weights = []
        component_returns = []
        component_volatilities = []

        # VN-Index component
        if long_analyzer and hasattr(long_analyzer, 'analysis_results') and long_analyzer.analysis_results:
            if 'VNINDEX' in long_analyzer.analysis_results:
                vnindex_metrics = long_analyzer.analysis_results['VNINDEX']
                component_returns.append(vnindex_metrics['annualized_return'])
                component_volatilities.append(vnindex_metrics['volatility'])
                component_weights.append(0.20)  # 20% VN-Index
                component_count += 1

            if 'PNJ' in long_analyzer.analysis_results:
                pnj_metrics = long_analyzer.analysis_results['PNJ']
                component_returns.append(pnj_metrics['annualized_return'])
                component_volatilities.append(pnj_metrics['volatility'])
                component_weights.append(0.15)  # 15% PNJ (Gold exposure)
                component_count += 1

        # USD/VND component
        if usd_vnd_analyzer and hasattr(usd_vnd_analyzer, 'analysis_results') and usd_vnd_analyzer.analysis_results:
            usd_metrics = usd_vnd_analyzer.analysis_results
            component_returns.append(usd_metrics['annualized_return'])
            component_volatilities.append(usd_metrics['volatility'])
            component_weights.append(0.20)  # 20% USD/VND
            component_count += 1

        # ETF component
        if good_etfs is not None and not good_etfs.empty:
            best_etf = good_etfs.iloc[0]
            component_returns.append(best_etf['one_year_return'])
            component_volatilities.append(best_etf['volatility'])
            component_weights.append(0.25)  # 25% Best ETF
            component_count += 1

        # Short bad stocks component
        if bad_stocks is not None and not bad_stocks.empty:
            component_returns.append(0.06)  # 6% from shorting bad stocks
            component_volatilities.append(0.22)
            component_weights.append(0.10)  # 10% Short positions
            component_count += 1

        # Calculate combined portfolio metrics
        if component_count > 0:
            # Normalize weights
            total_weight = sum(component_weights)
            if total_weight > 0:
                component_weights = [w/total_weight * 0.8 for w in component_weights]  # 80% in strategies, 20% cash

            # Weighted portfolio return
            combined_return = sum(r * w for r, w in zip(component_returns, component_weights))

            # Portfolio volatility (simplified diversification)
            portfolio_variance = sum((w * vol)**2 for w, vol in zip(component_weights, component_volatilities))
            combined_volatility = (portfolio_variance ** 0.5) * 0.7  # Diversification benefit

            # Portfolio Sharpe ratio
            combined_sharpe = (combined_return - 0.03) / combined_volatility if combined_volatility > 0 else 0

            # Estimate other metrics
            combined_max_drawdown = -combined_volatility * 1.8
            combined_expected_value = combined_return * 0.95
            combined_win_rate = 0.55  # Reasonable estimate for diversified portfolio

        # Create single comprehensive strategy
        self.strategy_results["Comprehensive Vietnamese Portfolio"] = {
            'annualized_return': combined_return,
            'volatility': combined_volatility,
            'sharpe_ratio': combined_sharpe,
            'max_drawdown': combined_max_drawdown,
            'expected_value_annual': combined_expected_value,
            'win_rate': combined_win_rate,
            'components': component_count,
            'diversification': 'HIGH' if component_count >= 4 else 'MODERATE' if component_count >= 2 else 'LOW'
        }

        # Single allocation: 80% in comprehensive strategy, 20% cash
        self.allocation_weights = {
            "Comprehensive Vietnamese Portfolio": 0.80,
            "Cash": 0.20
        }



    def calculate_portfolio_performance(self):
        """
        Calculate combined portfolio performance metrics
        """
        if not self.strategy_results or not self.allocation_weights:
            print("Insufficient data for portfolio calculation")
            return None

        # Calculate weighted portfolio metrics
        portfolio_return = 0
        portfolio_volatility = 0
        portfolio_expected_value = 0
        weighted_sharpe = 0
        weighted_max_drawdown = 0

        strategy_weights = {k: v for k, v in self.allocation_weights.items() if k != 'Cash'}
        total_strategy_weight = sum(strategy_weights.values())

        if total_strategy_weight > 0:
            for strategy, weight in strategy_weights.items():
                if strategy in self.strategy_results:
                    metrics = self.strategy_results[strategy]

                    # Weight the metrics
                    portfolio_return += weight * metrics.get('annualized_return', 0)
                    portfolio_volatility += (weight ** 2) * (metrics.get('volatility', 0) ** 2)
                    portfolio_expected_value += weight * metrics.get('expected_value_annual', 0)
                    weighted_sharpe += weight * metrics.get('sharpe_ratio', 0)
                    weighted_max_drawdown += weight * abs(metrics.get('max_drawdown', 0))

        # Add cash return (assume 3% risk-free rate)
        cash_weight = self.allocation_weights.get('Cash', 0)
        cash_return = 0.03  # 3% risk-free rate
        portfolio_return += cash_weight * cash_return

        # Portfolio volatility (simplified - assumes some correlation)
        portfolio_volatility = np.sqrt(portfolio_volatility) * 0.8  # Correlation adjustment

        # Portfolio Sharpe ratio
        portfolio_sharpe = (portfolio_return - 0.03) / portfolio_volatility if portfolio_volatility > 0 else 0

        # Portfolio max drawdown (conservative estimate)
        portfolio_max_drawdown = -weighted_max_drawdown * 0.7  # Diversification benefit

        # Additional important risk metrics
        # Sortino Ratio (downside deviation focus)
        downside_returns = []
        for strategy, weight in strategy_weights.items():
            if strategy in self.strategy_results:
                metrics = self.strategy_results[strategy]
                # Estimate downside volatility (conservative approach)
                downside_vol = metrics.get('volatility', 0) * 0.7  # Assume 70% of vol is downside
                downside_returns.append(weight * downside_vol)

        portfolio_downside_vol = np.sqrt(sum([x**2 for x in downside_returns])) * 0.8
        portfolio_sortino = (portfolio_return - 0.03) / portfolio_downside_vol if portfolio_downside_vol > 0 else 0

        # Information Ratio (excess return per unit of tracking error)
        # Using VN-Index as benchmark
        benchmark_return = 0.06  # Assume 6% VN-Index return
        tracking_error = portfolio_volatility * 0.5  # Conservative estimate
        portfolio_info_ratio = (portfolio_return - benchmark_return) / tracking_error if tracking_error > 0 else 0

        # Value at Risk (95% confidence)
        portfolio_var_95 = portfolio_return - (1.645 * portfolio_volatility)  # 95% VaR

        # Maximum Drawdown to Volatility Ratio (risk-adjusted drawdown)
        dd_vol_ratio = abs(portfolio_max_drawdown) / portfolio_volatility if portfolio_volatility > 0 else 0

        self.portfolio_metrics = {
            'portfolio_return': portfolio_return,
            'portfolio_volatility': portfolio_volatility,
            'portfolio_sharpe': portfolio_sharpe,
            'portfolio_sortino': portfolio_sortino,
            'portfolio_info_ratio': portfolio_info_ratio,
            'portfolio_expected_value': portfolio_expected_value,
            'portfolio_max_drawdown': portfolio_max_drawdown,
            'portfolio_var_95': portfolio_var_95,
            'dd_volatility_ratio': dd_vol_ratio,
            'cash_allocation': cash_weight,
            'strategy_allocation': total_strategy_weight
        }

        return self.portfolio_metrics

    def display_comprehensive_summary(self):
        """
        Display single comprehensive strategy summary
        """
        print("\n" + "="*60)
        print("COMPREHENSIVE VIETNAMESE PORTFOLIO STRATEGY")
        print("="*60)

        # Get the comprehensive strategy
        comp_strategy = self.strategy_results.get("Comprehensive Vietnamese Portfolio", {})
        cash_allocation = self.allocation_weights.get("Cash", 0)
        strategy_allocation = self.allocation_weights.get("Comprehensive Vietnamese Portfolio", 0)

        print(f"\nSTRATEGY COMPOSITION:")
        print("-" * 30)
        print(f"  Comprehensive Strategy: {strategy_allocation:.1%}")
        print(f"    ├─ VN-Index (20%): Vietnamese market exposure")
        print(f"    ├─ PNJ Gold (15%): Gold/precious metals exposure")
        print(f"    ├─ USD/VND (20%): Currency hedge")
        print(f"    ├─ Best ETF (25%): Diversified market exposure")
        print(f"    └─ Short Bad Stocks (10%): Short selling opportunities")
        print(f"  Cash Reserve: {cash_allocation:.1%}")

        # Strategy performance metrics
        if comp_strategy:
            print(f"\nSTRATEGY PERFORMANCE:")
            print("-" * 25)
            print(f"  Expected Return: {comp_strategy['annualized_return']:.2%}")
            print(f"  Volatility: {comp_strategy['volatility']:.2%}")
            print(f"  Sharpe Ratio: {comp_strategy['sharpe_ratio']:.3f}")
            print(f"  Max Drawdown: {comp_strategy['max_drawdown']:.2%}")
            print(f"  Win Rate: {comp_strategy['win_rate']:.1%}")
            print(f"  Components: {comp_strategy['components']} asset classes")
            print(f"  Diversification: {comp_strategy['diversification']}")

        # Portfolio-level metrics
        if self.portfolio_metrics:
            print(f"\nPORTFOLIO METRICS:")
            print("-" * 20)
            print(f"  Expected Return: {self.portfolio_metrics['portfolio_return']:.2%}")
            print(f"  Volatility: {self.portfolio_metrics['portfolio_volatility']:.2%}")
            print(f"  Sharpe Ratio: {self.portfolio_metrics['portfolio_sharpe']:.3f}")
            print(f"  Sortino Ratio: {self.portfolio_metrics['portfolio_sortino']:.3f}")
            print(f"  Information Ratio: {self.portfolio_metrics['portfolio_info_ratio']:.3f}")
            print(f"  Max Drawdown: {self.portfolio_metrics['portfolio_max_drawdown']:.2%}")
            print(f"  VaR (95%): {self.portfolio_metrics['portfolio_var_95']:.2%}")
            print(f"  DD/Vol Ratio: {self.portfolio_metrics['dd_volatility_ratio']:.2f}")

            # Risk assessment
            sharpe = self.portfolio_metrics['portfolio_sharpe']

            risk_rating = "EXCELLENT" if sharpe > 1.0 else "GOOD" if sharpe > 0.5 else "MODERATE" if sharpe > 0 else "POOR"
            risk_profile = "CONSERVATIVE" if cash_allocation > 0.4 else "MODERATE" if cash_allocation > 0.2 else "AGGRESSIVE"

            print(f"\nOVERALL ASSESSMENT:")
            print(f"  Risk Profile: {risk_profile}")
            print(f"  Performance Rating: {risk_rating}")
            if comp_strategy:
                print(f"  Diversification: {comp_strategy['diversification']}")
            print(f"  Cash Buffer: {cash_allocation:.1%} (Risk Management)")




def generate_portfolio_summary(long_analyzer=None, usd_vnd_analyzer=None,
                             bad_stocks=None, good_etfs=None):
    """
    Generate comprehensive portfolio summary combining ALL strategies
    """
    portfolio_summary = PortfolioSummaryAnalyzer()

    # Collect all strategy results including shorts, ETFs, and gold
    portfolio_summary.collect_strategy_results(long_analyzer, usd_vnd_analyzer,
                                             bad_stocks, good_etfs)

    # Calculate portfolio performance
    portfolio_metrics = portfolio_summary.calculate_portfolio_performance()

    if portfolio_metrics:
        # Display comprehensive summary
        portfolio_summary.display_comprehensive_summary()
        return portfolio_summary
    else:
        print("Failed to calculate portfolio performance metrics.")
        return None


class KellyCriterionOptimizer:
    def __init__(self):
        self.strategies = {}
        self.kelly_fractions = {}
        self.optimal_allocation = {}

    def add_strategy_performance(self, strategy_name, win_rate, avg_win, avg_loss, sharpe_ratio=None):
        """
        Add strategy performance metrics for Kelly calculation
        """
        kelly_fraction = self.calculate_kelly_fraction(win_rate, avg_win, avg_loss)

        self.strategies[strategy_name] = {
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'kelly_fraction': kelly_fraction,
            'sharpe_ratio': sharpe_ratio or 0
        }

        self.kelly_fractions[strategy_name] = kelly_fraction

        return kelly_fraction

    def calculate_kelly_fraction(self, win_rate, avg_win, avg_loss):
        """
        Calculate Kelly fraction for a given strategy
        Kelly% = (bp - q) / b
        where:
        b = odds received on the wager (avg_win/avg_loss)
        p = probability of winning (win_rate)
        q = probability of losing (1 - win_rate)
        """
        if avg_loss == 0 or avg_loss >= 0:  # avg_loss should be negative
            return 0

        b = abs(avg_win / avg_loss)  # Odds ratio
        p = win_rate
        q = 1 - win_rate

        kelly_fraction = (b * p - q) / b

        # Cap Kelly fraction at reasonable levels to avoid over-leverage
        return max(0, min(kelly_fraction, 0.25))  # Max 25% per strategy

    def calculate_optimal_allocation(self, total_capital=1.0, conservative_factor=0.5):
        """
        Calculate optimal portfolio allocation using Kelly Criterion
        """
        if not self.kelly_fractions:
            print("No strategies added for optimization.")
            return None

        # Apply conservative factor to Kelly fractions
        conservative_kelly = {k: v * conservative_factor for k, v in self.kelly_fractions.items()}

        # Calculate total allocation to strategies
        total_strategy_allocation = sum(conservative_kelly.values())

        # If total allocation exceeds 100%, scale down proportionally
        if total_strategy_allocation > total_capital:
            scaling_factor = total_capital * 0.8 / total_strategy_allocation  # Use 80% max
            conservative_kelly = {k: v * scaling_factor for k, v in conservative_kelly.items()}
            total_strategy_allocation = sum(conservative_kelly.values())

        # Calculate cash allocation
        cash_allocation = total_capital - total_strategy_allocation

        # Create final allocation
        self.optimal_allocation = conservative_kelly.copy()
        self.optimal_allocation['Cash'] = cash_allocation

        return self.optimal_allocation

    def display_optimization_results(self):
        """
        Display Kelly Criterion optimization results
        """
        if not self.strategies:
            print("No strategies to display.")
            return

        print("\nKELLY CRITERION OPTIMIZATION")
        print("=" * 35)

        if self.optimal_allocation:
            print("Optimal Allocation:")
            for asset, allocation in self.optimal_allocation.items():
                kelly_frac = self.kelly_fractions.get(asset, 0)
                print(f"  {asset}: {allocation:.1%} (Kelly: {kelly_frac:.1%})")

            cash_percentage = self.optimal_allocation.get('Cash', 0)
            risk_level = "CONSERVATIVE" if cash_percentage > 0.5 else "MODERATE" if cash_percentage > 0.3 else "AGGRESSIVE"
            print(f"Risk Profile: {risk_level}")


if __name__ == "__main__":
    print("VIETNAMESE MARKET PORTFOLIO OPTIMIZER")
    print("=" * 50)

    # Step 1: Load and screen bad performing stocks for shorting
    stocks_df, bad_stocks = load_and_screen_stocks()

    # Step 2: Analyze ETF opportunities for diversification
    etf_screener, good_etfs = analyze_etf_opportunities()

    # Step 3: Analyze long strategies (VN-Index and PNJ)
    long_analyzer = analyze_long_strategies()

    # Step 4: Analyze USD/VND long strategy
    usd_vnd_analyzer = analyze_usd_vnd_strategy()

    # Step 5: Optimize cash allocation using Kelly Criterion for ALL strategies
    kelly_optimizer = optimize_cash_allocation(long_analyzer, usd_vnd_analyzer, bad_stocks, good_etfs)

    # Step 6: Generate comprehensive portfolio summary combining ALL strategies
    portfolio_summary = generate_portfolio_summary(long_analyzer, usd_vnd_analyzer,
                                                  bad_stocks, good_etfs)

    print("\n" + "=" * 50)
    print("ANALYSIS COMPLETE")
    print("=" * 50)

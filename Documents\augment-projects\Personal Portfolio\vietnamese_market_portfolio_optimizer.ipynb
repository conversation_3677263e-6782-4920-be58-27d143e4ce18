{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "portfolio-header",
   "metadata": {},
   "source": [
    "# Vietnamese Market Portfolio Optimizer\n",
    "## Investment Thesis Implementation\n",
    "\n",
    "**Strategy Components:**\n",
    "1. Short really bad and low volume Vietnamese companies\n",
    "2. Long USD/VND currency pair\n",
    "3. Long gold while hedging with VN-Index\n",
    "4. Optimal cash allocation using Kelly Criterion\n",
    "\n",
    "**Data Sources Needed:**\n",
    "- Vietnamese stock market data (Ho Chi Minh Stock Exchange - HOSE, Hanoi Stock Exchange - HNX)\n",
    "- USD/VND exchange rate data\n",
    "- Gold prices (XAU/USD or local gold prices)\n",
    "- VN-Index data for hedging calculations"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "imports",
   "metadata": {},
   "outputs": [],
   "source": [
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from datetime import datetime, timedelta\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# For statistical analysis\n",
    "from scipy import stats\n",
    "from sklearn.preprocessing import StandardScaler\n",
    "from sklearn.cluster import KMeans\n",
    "\n",
    "# For financial calculations\n",
    "import yfinance as yf  # For some international data\n",
    "# Note: You'll need Vietnamese market data APIs like:\n",
    "# - SSI Securities API\n",
    "# - VND Direct API\n",
    "# - Or web scraping from cafef.vn, vietstock.vn\n",
    "\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "data-collection",
   "metadata": {},
   "source": [
    "## 1. Data Collection Framework\n",
    "\n",
    "**You'll need to implement data collection from:**\n",
    "- Vietnamese stock exchanges (HOSE, HNX, UPCOM)\n",
    "- Currency data (USD/VND)\n",
    "- Gold prices\n",
    "- VN-Index historical data"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "data-collection-functions",
   "metadata": {},
   "outputs": [],
   "source": [
    "class VietnameseMarketData:\n",
    "    def __init__(self):\n",
    "        self.stocks_data = None\n",
    "        self.currency_data = None\n",
    "        self.gold_data = None\n",
    "        self.vnindex_data = None\n",
    "    \n",
    "    def load_vietnamese_stocks(self, file_path=None):\n",
    "        \"\"\"\n",
    "        Load Vietnamese stock data\n",
    "        You'll need to implement this with actual data sources\n",
    "        \"\"\"\n",
    "        # Placeholder - replace with actual data loading\n",
    "        if file_path:\n",
    "            self.stocks_data = pd.read_csv(file_path)\n",
    "        else:\n",
    "            print(\"Please provide Vietnamese stock data\")\n",
    "            # Example structure of what the data should look like:\n",
    "            sample_data = {\n",
    "                'symbol': ['VIC', 'VHM', 'VCB', 'BID', 'CTG'],\n",
    "                'price': [100000, 85000, 95000, 45000, 35000],\n",
    "                'volume': [1000000, 800000, 1200000, 500000, 300000],\n",
    "                'market_cap': [500e9, 400e9, 600e9, 200e9, 150e9],\n",
    "                'pe_ratio': [15.2, 12.8, 8.5, 6.2, 18.5],\n",
    "                'debt_to_equity': [0.3, 0.8, 0.1, 0.2, 0.4],\n",
    "                'roe': [0.15, 0.12, 0.18, 0.14, 0.08],\n",
    "                'revenue_growth': [0.1, -0.05, 0.08, 0.12, -0.15]\n",
    "            }\n",
    "            self.stocks_data = pd.DataFrame(sample_data)\n",
    "            print(\"Using sample data. Replace with actual Vietnamese market data.\")\n",
    "        \n",
    "        return self.stocks_data\n",
    "    \n",
    "    def load_usd_vnd_data(self):\n",
    "        \"\"\"\n",
    "        Load USD/VND exchange rate data\n",
    "        \"\"\"\n",
    "        try:\n",
    "            # Try to get some USD/VND data (this might not work perfectly)\n",
    "            # You'll need a proper forex data source\n",
    "            dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')\n",
    "            # Simulated USD/VND rates (replace with real data)\n",
    "            rates = np.random.normal(24000, 200, len(dates))\n",
    "            self.currency_data = pd.DataFrame({\n",
    "                'date': dates,\n",
    "                'usd_vnd': rates\n",
    "            })\n",
    "            print(\"Using simulated USD/VND data. Replace with actual forex data.\")\n",
    "        except:\n",
    "            print(\"Could not load USD/VND data. Please implement proper forex data source.\")\n",
    "        \n",
    "        return self.currency_data\n",
    "    \n",
    "    def load_gold_data(self):\n",
    "        \"\"\"\n",
    "        Load gold price data\n",
    "        \"\"\"\n",
    "        try:\n",
    "            # Get gold data from Yahoo Finance\n",
    "            gold = yf.download('GC=F', start='2023-01-01', end='2024-12-31')\n",
    "            self.gold_data = gold[['Close']].reset_index()\n",
    "            self.gold_data.columns = ['date', 'gold_price']\n",
    "            print(\"Gold data loaded successfully\")\n",
    "        except:\n",
    "            print(\"Could not load gold data from Yahoo Finance\")\n",
    "        \n",
    "        return self.gold_data\n",
    "    \n",
    "    def load_vnindex_data(self):\n",
    "        \"\"\"\n",
    "        Load VN-Index data for hedging calculations\n",
    "        \"\"\"\n",
    "        # Placeholder - you'll need actual VN-Index data\n",
    "        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')\n",
    "        # Simulated VN-Index values\n",
    "        vnindex_values = np.random.normal(1200, 50, len(dates))\n",
    "        self.vnindex_data = pd.DataFrame({\n",
    "            'date': dates,\n",
    "            'vnindex': vnindex_values\n",
    "        })\n",
    "        print(\"Using simulated VN-Index data. Replace with actual index data.\")\n",
    "        \n",
    "        return self.vnindex_data\n",
    "\n",
    "# Initialize data loader\n",
    "market_data = VietnameseMarketData()"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "screening-section",
   "metadata": {},
   "source": [
    "## 2. Stock Screening for Short Candidates\n",
    "\n",
    "Identify \"really bad and low volume\" companies for shorting"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "stock-screening",
   "metadata": {},
   "outputs": [],
   "source": [
    "class StockScreener:\n",
    "    def __init__(self, stocks_data):\n",
    "        self.stocks_data = stocks_data\n",
    "    \n",
    "    def screen_short_candidates(self, \n",
    "                              volume_percentile=20,  # Bottom 20% by volume\n",
    "                              pe_threshold=30,       # High P/E ratios\n",
    "                              debt_threshold=0.7,    # High debt\n",
    "                              roe_threshold=0.05,    # Low ROE\n",
    "                              revenue_growth_threshold=-0.1):  # Negative growth\n",
    "        \"\"\"\n",
    "        Screen for stocks that are good short candidates\n",
    "        \"\"\"\n",
    "        df = self.stocks_data.copy()\n",
    "        \n",
    "        # Calculate volume percentile threshold\n",
    "        volume_threshold = df['volume'].quantile(volume_percentile/100)\n",
    "        \n",
    "        # Apply screening criteria\n",
    "        short_candidates = df[\n",
    "            (df['volume'] <= volume_threshold) &  # Low volume\n",
    "            (\n",
    "                (df['pe_ratio'] > pe_threshold) |  # High P/E OR\n",
    "                (df['debt_to_equity'] > debt_threshold) |  # High debt OR\n",
    "                (df['roe'] < roe_threshold) |  # Low ROE OR\n",
    "                (df['revenue_growth'] < revenue_growth_threshold)  # Negative growth\n",
    "            )\n",
    "        ]\n",
    "        \n",
    "        # Calculate a \"badness score\"\n",
    "        short_candidates['badness_score'] = (\n",
    "            (short_candidates['pe_ratio'] / df['pe_ratio'].median()) +\n",
    "            (short_candidates['debt_to_equity'] / df['debt_to_equity'].median()) +\n",
    "            (1 / (short_candidates['roe'] + 0.01)) +  # Inverse ROE (higher is worse)\n",
    "            (1 / (short_candidates['volume'] / df['volume'].median()))  # Inverse relative volume\n",
    "        )\n",
    "        \n",
    "        return short_candidates.sort_values('badness_score', ascending=False)\n",
    "    \n",
    "    def visualize_screening_results(self, short_candidates):\n",
    "        \"\"\"\n",
    "        Visualize the screening results\n",
    "        \"\"\"\n",
    "        fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n",
    "        \n",
    "        # Volume vs Market Cap\n",
    "        axes[0,0].scatter(self.stocks_data['volume'], self.stocks_data['market_cap'], \n",
    "                         alpha=0.6, label='All stocks')\n",
    "        axes[0,0].scatter(short_candidates['volume'], short_candidates['market_cap'], \n",
    "                         color='red', alpha=0.8, label='Short candidates')\n",
    "        axes[0,0].set_xlabel('Volume')\n",
    "        axes[0,0].set_ylabel('Market Cap')\n",
    "        axes[0,0].set_title('Volume vs Market Cap')\n",
    "        axes[0,0].legend()\n",
    "        axes[0,0].set_xscale('log')\n",
    "        axes[0,0].set_yscale('log')\n",
    "        \n",
    "        # P/E Ratio distribution\n",
    "        axes[0,1].hist(self.stocks_data['pe_ratio'], bins=20, alpha=0.6, label='All stocks')\n",
    "        axes[0,1].hist(short_candidates['pe_ratio'], bins=10, alpha=0.8, color='red', label='Short candidates')\n",
    "        axes[0,1].set_xlabel('P/E Ratio')\n",
    "        axes[0,1].set_ylabel('Frequency')\n",
    "        axes[0,1].set_title('P/E Ratio Distribution')\n",
    "        axes[0,1].legend()\n",
    "        \n",
    "        # Debt to Equity vs ROE\n",
    "        axes[1,0].scatter(self.stocks_data['debt_to_equity'], self.stocks_data['roe'], \n",
    "                         alpha=0.6, label='All stocks')\n",
    "        axes[1,0].scatter(short_candidates['debt_to_equity'], short_candidates['roe'], \n",
    "                         color='red', alpha=0.8, label='Short candidates')\n",
    "        axes[1,0].set_xlabel('Debt to Equity')\n",
    "        axes[1,0].set_ylabel('ROE')\n",
    "        axes[1,0].set_title('Debt to Equity vs ROE')\n",
    "        axes[1,0].legend()\n",
    "        \n",
    "        # Badness Score\n",
    "        axes[1,1].bar(range(len(short_candidates)), short_candidates['badness_score'])\n",
    "        axes[1,1].set_xlabel('Stock Rank')\n",
    "        axes[1,1].set_ylabel('Badness Score')\n",
    "        axes[1,1].set_title('Short Candidate Badness Scores')\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "        \n",
    "        return fig\n",
    "\n",
    "# Load sample data and run screening\n",
    "stocks_df = market_data.load_vietnamese_stocks()\n",
    "screener = StockScreener(stocks_df)\n",
    "short_candidates = screener.screen_short_candidates()\n",
    "\n",
    "print(\"Top Short Candidates:\")\n",
    "print(short_candidates[['symbol', 'volume', 'pe_ratio', 'debt_to_equity', 'roe', 'badness_score']].head())"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "currency-analysis",
   "metadata": {},
   "source": [
    "## 3. USD/VND Currency Analysis\n",
    "\n",
    "Analyze USD/VND trends for long position sizing"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "currency-strategy",
   "metadata": {},
   "outputs": [],
   "source": [
    "class CurrencyAnalyzer:\n",
    "    def __init__(self, currency_data):\n",
    "        self.currency_data = currency_data\n",
    "    \n",
    "    def analyze_usd_vnd_trend(self, lookback_days=252):\n",
    "        \"\"\"\n",
    "        Analyze USD/VND trend and momentum\n",
    "        \"\"\"\n",
    "        df = self.currency_data.copy()\n",
    "        df = df.sort_values('date')\n",
    "        \n",
    "        # Calculate moving averages\n",
    "        df['ma_20'] = df['usd_vnd'].rolling(20).mean()\n",
    "        df['ma_50'] = df['usd_vnd'].rolling(50).mean()\n",
    "        df['ma_200'] = df['usd_vnd'].rolling(200).mean()\n",
    "        \n",
    "        # Calculate returns\n",
    "        df['daily_return'] = df['usd_vnd'].pct_change()\n",
    "        df['volatility'] = df['daily_return'].rolling(30).std() * np.sqrt(252)\n",
    "        \n",
    "        # Trend signals\n",
    "        df['trend_signal'] = np.where(\n",
    "            (df['usd_vnd'] > df['ma_20']) & \n",
    "            (df['ma_20'] > df['ma_50']) & \n",
    "            (df['ma_50'] > df['ma_200']), 1, 0\n",
    "        )\n",
    "        \n",
    "        return df\n",
    "    \n",
    "    def calculate_position_size(self, df, risk_per_trade=0.02):\n",
    "        \"\"\"\n",
    "        Calculate optimal USD/VND position size based on volatility\n",
    "        \"\"\"\n",
    "        current_volatility = df['volatility'].iloc[-1]\n",
    "        position_size = risk_per_trade / current_volatility\n",
    "        \n",
    "        return min(position_size, 0.25)  # Cap at 25% of portfolio\n",
    "    \n",
    "    def plot_currency_analysis(self, df):\n",
    "        \"\"\"\n",
    "        Plot USD/VND analysis\n",
    "        \"\"\"\n",
    "        fig, axes = plt.subplots(3, 1, figsize=(15, 12))\n",
    "        \n",
    "        # Price and moving averages\n",
    "        axes[0].plot(df['date'], df['usd_vnd'], label='USD/VND', linewidth=1)\n",
    "        axes[0].plot(df['date'], df['ma_20'], label='MA 20', alpha=0.7)\n",
    "        axes[0].plot(df['date'], df['ma_50'], label='MA 50', alpha=0.7)\n",
    "        axes[0].plot(df['date'], df['ma_200'], label='MA 200', alpha=0.7)\n",
    "        axes[0].set_title('USD/VND Exchange Rate with Moving Averages')\n",
    "        axes[0].legend()\n",
    "        axes[0].grid(True, alpha=0.3)\n",
    "        \n",
    "        # Volatility\n",
    "        axes[1].plot(df['date'], df['volatility'], color='orange', label='30-day Volatility')\n",
    "        axes[1].set_title('USD/VND Volatility (Annualized)')\n",
    "        axes[1].legend()\n",
    "        axes[1].grid(True, alpha=0.3)\n",
    "        \n",
    "        # Trend signals\n",
    "        axes[2].fill_between(df['date'], 0, df['trend_signal'], \n",
    "                           alpha=0.3, color='green', label='Long Signal')\n",
    "        axes[2].set_title('USD/VND Trend Signals')\n",
    "        axes[2].set_ylabel('Signal')\n",
    "        axes[2].legend()\n",
    "        axes[2].grid(True, alpha=0.3)\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "        \n",
    "        return fig\n",
    "\n",
    "# Load and analyze currency data\n",
    "currency_df = market_data.load_usd_vnd_data()\n",
    "currency_analyzer = CurrencyAnalyzer(currency_df)\n",
    "currency_analysis = currency_analyzer.analyze_usd_vnd_trend()\n",
    "usd_vnd_position_size = currency_analyzer.calculate_position_size(currency_analysis)\n",
    "\n",
    "print(f\"Recommended USD/VND position size: {usd_vnd_position_size:.2%} of portfolio\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "gold-hedge-section",
   "metadata": {},
   "source": [
    "## 4. Gold Position with VN-Index Hedge\n",
    "\n",
    "Implement gold long position with VN-Index hedge ratio calculation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "gold-hedge-strategy",
   "metadata": {},
   "outputs": [],
   "source": [
    "class GoldHedgeStrategy:\n",
    "    def __init__(self, gold_data, vnindex_data):\n",
    "        self.gold_data = gold_data\n",
    "        self.vnindex_data = vnindex_data\n",
    "    \n",
    "    def calculate_hedge_ratio(self, lookback_days=252):\n",
    "        \"\"\"\n",
    "        Calculate optimal hedge ratio between gold and VN-Index\n",
    "        \"\"\"\n",
    "        # Merge data on date\n",
    "        merged_data = pd.merge(self.gold_data, self.vnindex_data, on='date', how='inner')\n",
    "        merged_data = merged_data.sort_values('date')\n",
    "        \n",
    "        # Calculate returns\n",
    "        merged_data['gold_return'] = merged_data['gold_price'].pct_change()\n",
    "        merged_data['vnindex_return'] = merged_data['vnindex'].pct_change()\n",
    "        \n",
    "        # Remove NaN values\n",
    "        merged_data = merged_data.dropna()\n",
    "        \n",
    "        # Calculate rolling correlation and beta\n",
    "        merged_data['correlation'] = merged_data['gold_return'].rolling(lookback_days).corr(\n",
    "            merged_data['vnindex_return']\n",
    "        )\n",
    "        \n",
    "        # Calculate beta (hedge ratio)\n",
    "        def rolling_beta(window):\n",
    "            gold_ret = merged_data['gold_return'].rolling(window)\n",
    "            vn_ret = merged_data['vnindex_return'].rolling(window)\n",
    "            \n",
    "            covariance = gold_ret.cov(vn_ret)\n",
    "            vn_variance = vn_ret.var()\n",
    "            \n",
    "            return covariance / vn_variance\n",
    "        \n",
    "        merged_data['hedge_ratio'] = rolling_beta(lookback_days)\n",
    "        \n",
    "        return merged_data\n",
    "    \n",
    "    def calculate_hedged_portfolio_performance(self, hedge_data, gold_weight=0.15):\n",
    "        \"\"\"\n",
    "        Calculate performance of hedged gold position\n",
    "        \"\"\"\n",
    "        df = hedge_data.copy()\n",
    "        \n",
    "        # Calculate hedged returns\n",
    "        df['hedged_return'] = (\n",
    "            df['gold_return'] - \n",
    "            df['hedge_ratio'] * df['vnindex_return']\n",
    "        )\n",
    "        \n",
    "        # Portfolio metrics\n",
    "        hedged_sharpe = df['hedged_return'].mean() / df['hedged_return'].std() * np.sqrt(252)\n",
    "        unhedged_sharpe = df['gold_return'].mean() / df['gold_return'].std() * np.sqrt(252)\n",
    "        \n",
    "        # Volatility reduction\n",
    "        hedged_vol = df['hedged_return'].std() * np.sqrt(252)\n",
    "        unhedged_vol = df['gold_return'].std() * np.sqrt(252)\n",
    "        \n",
    "        metrics = {\n",
    "            'hedged_sharpe': hedged_sharpe,\n",
    "            'unhedged_sharpe': unhedged_sharpe,\n",
    "            'hedged_volatility': hedged_vol,\n",
    "            'unhedged_volatility': unhedged_vol,\n",
    "            'volatility_reduction': (unhedged_vol - hedged_vol) / unhedged_vol,\n",
    "            'current_hedge_ratio': df['hedge_ratio'].iloc[-1]\n",
    "        }\n",
    "        \n",
    "        return df, metrics\n",
    "    \n",
    "    def plot_hedge_analysis(self, hedge_data, metrics):\n",
    "        \"\"\"\n",
    "        Plot gold hedge analysis\n",
    "        \"\"\"\n",
    "        fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n",
    "        \n",
    "        # Gold vs VN-Index prices (normalized)\n",
    "        gold_norm = hedge_data['gold_price'] / hedge_data['gold_price'].iloc[0]\n",
    "        vn_norm = hedge_data['vnindex'] / hedge_data['vnindex'].iloc[0]\n",
    "        \n",
    "        axes[0,0].plot(hedge_data['date'], gold_norm, label='Gold (normalized)', color='gold')\n",
    "        axes[0,0].plot(hedge_data['date'], vn_norm, label='VN-Index (normalized)', color='blue')\n",
    "        axes[0,0].set_title('Gold vs VN-Index Performance')\n",
    "        axes[0,0].legend()\n",
    "        axes[0,0].grid(True, alpha=0.3)\n",
    "        \n",
    "        # Rolling correlation\n",
    "        axes[0,1].plot(hedge_data['date'], hedge_data['correlation'], color='purple')\n",
    "        axes[0,1].set_title('Rolling Correlation (Gold vs VN-Index)')\n",
    "        axes[0,1].set_ylabel('Correlation')\n",
    "        axes[0,1].grid(True, alpha=0.3)\n",
    "        \n",
    "        # Hedge ratio over time\n",
    "        axes[1,0].plot(hedge_data['date'], hedge_data['hedge_ratio'], color='red')\n",
    "        axes[1,0].set_title('Optimal Hedge Ratio Over Time')\n",
    "        axes[1,0].set_ylabel('Hedge Ratio')\n",
    "        axes[1,0].grid(True, alpha=0.3)\n",
    "        \n",
    "        # Hedged vs unhedged returns\n",
    "        cumulative_hedged = (1 + hedge_data['hedged_return']).cumprod()\n",
    "        cumulative_unhedged = (1 + hedge_data['gold_return']).cumprod()\n",
    "        \n",
    "        axes[1,1].plot(hedge_data['date'], cumulative_hedged, label='Hedged Gold', color='green')\n",
    "        axes[1,1].plot(hedge_data['date'], cumulative_unhedged, label='Unhedged Gold', color='gold')\n",
    "        axes[1,1].set_title('Cumulative Returns: Hedged vs Unhedged')\n",
    "        axes[1,1].legend()\n",
    "        axes[1,1].grid(True, alpha=0.3)\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "        \n",
    "        # Print metrics\n",
    "        print(\"\\nGold Hedge Strategy Metrics:\")\n",
    "        print(f\"Current Hedge Ratio: {metrics['current_hedge_ratio']:.3f}\")\n",
    "        print(f\"Hedged Sharpe Ratio: {metrics['hedged_sharpe']:.3f}\")\n",
    "        print(f\"Unhedged Sharpe Ratio: {metrics['unhedged_sharpe']:.3f}\")\n",
    "        print(f\"Volatility Reduction: {metrics['volatility_reduction']:.1%}\")\n",
    "        \n",
    "        return fig\n",
    "\n",
    "# Load data and run gold hedge analysis\n",
    "gold_df = market_data.load_gold_data()\n",
    "vnindex_df = market_data.load_vnindex_data()\n",
    "\n",
    "gold_hedge = GoldHedgeStrategy(gold_df, vnindex_df)\n",
    "hedge_analysis = gold_hedge.calculate_hedge_ratio()\n",
    "hedge_performance, hedge_metrics = gold_hedge.calculate_hedged_portfolio_performance(hedge_analysis)\n",
    "\n",
    "print(f\"Recommended VN-Index hedge ratio for gold position: {hedge_metrics['current_hedge_ratio']:.3f}\")
   ]
  },
  {
   "cell_type": "markdown",
   "id": "kelly-criterion-section",
   "metadata": {},
   "source": [
    "## 5. Kelly Criterion for Optimal Cash Allocation\n",
    "\n",
    "Use Kelly Criterion to determine optimal position sizes and cash reserves"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "kelly-criterion",
   "metadata": {},
   "outputs": [],
   "source": [
    "class KellyCriterionOptimizer:\n",
    "    def __init__(self):\n",
    "        self.strategies = {}\n",
    "        self.kelly_fractions = {}\n",
    "    \n",
    "    def calculate_kelly_fraction(self, win_rate, avg_win, avg_loss):\n",
    "        \"\"\"\n",
    "        Calculate Kelly fraction for a given strategy\n",
    "        Kelly% = (bp - q) / b\n",
    "        where:\n",
    "        b = odds received on the wager (avg_win/avg_loss)\n",
    "        p = probability of winning (win_rate)\n",
    "        q = probability of losing (1 - win_rate)\n",
    "        \"\"\"\n",
    "        if avg_loss == 0:\n",
    "            return 0\n",
    "        \n",
    "        b = abs(avg_win / avg_loss)  # Odds ratio\n",
    "        p = win_rate\n",
    "        q = 1 - win_rate\n",
    "        \n",
    "        kelly_fraction = (b * p - q) / b\n",
    "        \n",
    "        # Cap Kelly fraction at reasonable levels to avoid over-leverage\n",
    "        return max(0, min(kelly_fraction, 0.25))  # Max 25% per strategy\n",
    "    \n",
    "    def analyze_strategy_performance(self, returns, strategy_name):\n",
    "        \"\"\"\n",
    "        Analyze historical performance of a strategy to calculate Kelly parameters\n",
    "        \"\"\"\n",
    "        returns = np.array(returns)\n",
    "        returns = returns[~np.isnan(returns)]  # Remove NaN values\n",
    "        \n",
    "        if len(returns) == 0:\n",
    "            return None\n",
    "        \n",
    "        # Separate wins and losses\n",
    "        wins = returns[returns > 0]\n",
    "        losses = returns[returns < 0]\n",
    "        \n",
    "        # Calculate statistics\n",
    "        win_rate = len(wins) / len(returns) if len(returns) > 0 else 0\n",
    "        avg_win = np.mean(wins) if len(wins) > 0 else 0\n",
    "        avg_loss = np.mean(losses) if len(losses) > 0 else 0\n",
    "        \n",
    "        # Calculate Kelly fraction\n",
    "        kelly_fraction = self.calculate_kelly_fraction(win_rate, avg_win, avg_loss)\n",
    "        \n",
    "        strategy_stats = {\n",
    "            'win_rate': win_rate,\n",
    "            'avg_win': avg_win,\n",
    "            'avg_loss': avg_loss,\n",
    "            'kelly_fraction': kelly_fraction,\n",
    "            'total_return': np.sum(returns),\n",
    "            'volatility': np.std(returns) * np.sqrt(252),\n",
    "            'sharpe_ratio': np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0\n",
    "        }\n",
    "        \n",
    "        self.strategies[strategy_name] = strategy_stats\n",
    "        self.kelly_fractions[strategy_name] = kelly_fraction\n",
    "        \n",
    "        return strategy_stats\n",
    "    \n",
    "    def calculate_optimal_portfolio_allocation(self, total_capital=1.0):\n",
    "        \"\"\"\n",
    "        Calculate optimal portfolio allocation based on Kelly fractions\n",
    "        \"\"\"\n",
    "        total_kelly = sum(self.kelly_fractions.values())\n",
    "        \n",
    "        # If total Kelly > 1, scale down proportionally\n",
    "        if total_kelly > 1:\n",
    "            scaling_factor = 0.8 / total_kelly  # Use 80% of Kelly to be conservative\n",
    "            scaled_kelly = {k: v * scaling_factor for k, v in self.kelly_fractions.items()}\n",
    "        else:\n",
    "            scaled_kelly = self.kelly_fractions.copy()\n",
    "        \n",
    "        # Calculate cash allocation\n",
    "        total_invested = sum(scaled_kelly.values())\n",
    "        cash_allocation = max(0, total_capital - total_invested)\n",
    "        \n",
    "        allocation = scaled_kelly.copy()\n",
    "        allocation['cash'] = cash_allocation\n",
    "        \n",
    "        return allocation\n",
    "    \n",
    "    def plot_kelly_analysis(self):\n",
    "        \"\"\"\n",
    "        Visualize Kelly Criterion analysis\n",
    "        \"\"\"\n",
    "        if not self.strategies:\n",
    "            print(\"No strategies analyzed yet.\")\n",
    "            return\n",
    "        \n",
    "        fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n",
    "        \n",
    "        strategies = list(self.strategies.keys())\n",
    "        kelly_fractions = [self.kelly_fractions[s] for s in strategies]\n",
    "        win_rates = [self.strategies[s]['win_rate'] for s in strategies]\n",
    "        sharpe_ratios = [self.strategies[s]['sharpe_ratio'] for s in strategies]\n",
    "        \n",
    "        # Kelly fractions by strategy\n",
    "        axes[0,0].bar(strategies, kelly_fractions, color='skyblue')\n",
    "        axes[0,0].set_title('Kelly Fractions by Strategy')\n",
    "        axes[0,0].set_ylabel('Kelly Fraction')\n",
    "        axes[0,0].tick_params(axis='x', rotation=45)\n",
    "        \n",
    "        # Win rates\n",
    "        axes[0,1].bar(strategies, win_rates, color='lightgreen')\n",
    "        axes[0,1].set_title('Win Rates by Strategy')\n",
    "        axes[0,1].set_ylabel('Win Rate')\n",
    "        axes[0,1].tick_params(axis='x', rotation=45)\n",
    "        \n",
    "        # Sharpe ratios\n",
    "        axes[1,0].bar(strategies, sharpe_ratios, color='orange')\n",
    "        axes[1,0].set_title('Sharpe Ratios by Strategy')\n",
    "        axes[1,0].set_ylabel('Sharpe Ratio')\n",
    "        axes[1,0].tick_params(axis='x', rotation=45)\n",
    "        \n",
    "        # Portfolio allocation pie chart\n",
    "        allocation = self.calculate_optimal_portfolio_allocation()\n",
    "        labels = list(allocation.keys())\n",
    "        sizes = list(allocation.values())\n",
    "        \n",
    "        axes[1,1].pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)\n",
    "        axes[1,1].set_title('Optimal Portfolio Allocation')\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "        \n",
    "        return fig\n",
    "\n",
    "# Initialize Kelly optimizer\n",
    "kelly_optimizer = KellyCriterionOptimizer()\n",
    "\n",
    "# Analyze each strategy (using simulated returns for demonstration)\n",
    "# In practice, you would use actual historical returns from your strategies\n",
    "\n",
    "# Short strategy returns (simulated)\n",
    "short_returns = np.random.normal(-0.001, 0.02, 252)  # Slightly negative mean, moderate volatility\n",
    "short_returns[short_returns > 0] *= 0.5  # Make wins smaller than losses\n",
    "kelly_optimizer.analyze_strategy_performance(short_returns, 'Short Bad Stocks')\n",
    "\n",
    "# USD/VND strategy returns (simulated)\n",
    "usd_vnd_returns = np.random.normal(0.0005, 0.015, 252)  # Slight positive drift\n",
    "kelly_optimizer.analyze_strategy_performance(usd_vnd_returns, 'Long USD/VND')\n",
    "\n",
    "# Hedged gold strategy returns (simulated)\n",
    "hedged_gold_returns = np.random.normal(0.0003, 0.012, 252)  # Lower volatility due to hedging\n",
    "kelly_optimizer.analyze_strategy_performance(hedged_gold_returns, 'Hedged Gold')\n",
    "\n",
    "# Calculate optimal allocation\n",
    "optimal_allocation = kelly_optimizer.calculate_optimal_portfolio_allocation()\n",
    "\n",
    "print(\"Kelly Criterion Analysis Results:\")\n",
    "print(\"=\" * 40)\n",
    "for strategy, stats in kelly_optimizer.strategies.items():\n",
    "    print(f\"\\n{strategy}:\")\n",
    "    print(f\"  Win Rate: {stats['win_rate']:.1%}\")\n",
    "    print(f\"  Avg Win: {stats['avg_win']:.3f}\")\n",
    "    print(f\"  Avg Loss: {stats['avg_loss']:.3f}\")\n",
    "    print(f\"  Kelly Fraction: {stats['kelly_fraction']:.1%}\")\n",
    "    print(f\"  Sharpe Ratio: {stats['sharpe_ratio']:.2f}\")\n",
    "\n",
    "print(f\"\\nOptimal Portfolio Allocation:\")\n",
    "print(\"=\" * 30)\n",
    "for asset, allocation in optimal_allocation.items():\n",
    "    print(f\"{asset}: {allocation:.1%}\")\n",
    "\n",
    "print(f\"\\nRecommended Cash Reserve: {optimal_allocation['cash']:.1%} of total capital\")
   ]
  },
  {
   "cell_type": "markdown",
   "id": "portfolio-performance-section",
   "metadata": {},
   "source": [
    "## 6. Portfolio Performance Analysis & Backtesting\n",
    "\n",
    "Comprehensive analysis of portfolio performance including Sharpe ratio, drawdowns, and risk metrics"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "portfolio-performance",
   "metadata": {},
   "outputs": [],
   "source": [
    "class PortfolioPerformanceAnalyzer:\n",
    "    def __init__(self, allocation_weights):\n",
    "        self.allocation_weights = allocation_weights\n",
    "        self.portfolio_returns = None\n",
    "        self.benchmark_returns = None\n",
    "        self.performance_metrics = {}\n",
    "    \n",
    "    def simulate_portfolio_returns(self, start_date='2023-01-01', end_date='2024-12-31', \n",
    "                                 initial_capital=100000):\n",
    "        \"\"\"\n",
    "        Simulate portfolio returns based on allocation weights\n",
    "        In practice, replace with actual historical data\n",
    "        \"\"\"\n",
    "        dates = pd.date_range(start=start_date, end=end_date, freq='D')\n",
    "        n_days = len(dates)\n",
    "        \n",
    "        # Simulate individual strategy returns\n",
    "        strategy_returns = {\n",
    "            'Short Bad Stocks': np.random.normal(-0.0002, 0.025, n_days),\n",
    "            'Long USD/VND': np.random.normal(0.0008, 0.018, n_days),\n",
    "            'Hedged Gold': np.random.normal(0.0005, 0.015, n_days),\n",
    "            'cash': np.random.normal(0.00015, 0.001, n_days)  # Cash returns (risk-free rate)\n",
    "        }\n",
    "        \n",
    "        # Calculate portfolio returns\n",
    "        portfolio_daily_returns = np.zeros(n_days)\n",
    "        for strategy, weight in self.allocation_weights.items():\n",
    "            if strategy in strategy_returns:\n",
    "                portfolio_daily_returns += weight * strategy_returns[strategy]\n",
    "        \n",
    "        # Create portfolio DataFrame\n",
    "        self.portfolio_returns = pd.DataFrame({\n",
    "            'date': dates,\n",
    "            'daily_return': portfolio_daily_returns,\n",
    "            'cumulative_return': (1 + portfolio_daily_returns).cumprod(),\n",
    "            'portfolio_value': initial_capital * (1 + portfolio_daily_returns).cumprod()\n",
    "        })\n",
    "        \n",
    "        # Simulate benchmark (VN-Index) returns\n",
    "        benchmark_returns = np.random.normal(0.0003, 0.020, n_days)\n",
    "        self.benchmark_returns = pd.DataFrame({\n",
    "            'date': dates,\n",
    "            'daily_return': benchmark_returns,\n",
    "            'cumulative_return': (1 + benchmark_returns).cumprod(),\n",
    "            'benchmark_value': initial_capital * (1 + benchmark_returns).cumprod()\n",
    "        })\n",
    "        \n",
    "        return self.portfolio_returns, self.benchmark_returns\n",
    "    \n",
    "    def calculate_performance_metrics(self, risk_free_rate=0.03):\n",
    "        \"\"\"\n",
    "        Calculate comprehensive performance metrics\n",
    "        \"\"\"\n",
    "        if self.portfolio_returns is None:\n",
    "            raise ValueError(\"Portfolio returns not calculated. Run simulate_portfolio_returns first.\")\n",
    "        \n",
    "        returns = self.portfolio_returns['daily_return']\n",
    "        benchmark_returns = self.benchmark_returns['daily_return']\n",
    "        \n",
    "        # Basic metrics\n",
    "        total_return = self.portfolio_returns['cumulative_return'].iloc[-1] - 1\n",
    "        annualized_return = (1 + total_return) ** (252 / len(returns)) - 1\n",
    "        volatility = returns.std() * np.sqrt(252)\n",
    "        \n",
    "        # Risk-adjusted metrics\n",
    "        sharpe_ratio = (annualized_return - risk_free_rate) / volatility\n",
    "        \n",
    "        # Drawdown analysis\n",
    "        cumulative_returns = self.portfolio_returns['cumulative_return']\n",
    "        running_max = cumulative_returns.expanding().max()\n",
    "        drawdown = (cumulative_returns - running_max) / running_max\n",
    "        max_drawdown = drawdown.min()\n",
    "        \n",
    "        # Calmar ratio\n",
    "        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0\n",
    "        \n",
    "        # Sortino ratio (downside deviation)\n",
    "        downside_returns = returns[returns < 0]\n",
    "        downside_deviation = downside_returns.std() * np.sqrt(252)\n",
    "        sortino_ratio = (annualized_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else 0\n",
    "        \n",
    "        # Beta and Alpha vs benchmark\n",
    "        covariance = np.cov(returns, benchmark_returns)[0, 1]\n",
    "        benchmark_variance = np.var(benchmark_returns)\n",
    "        beta = covariance / benchmark_variance if benchmark_variance > 0 else 0\n",
    "        \n",
    "        benchmark_annual_return = (1 + benchmark_returns.mean()) ** 252 - 1\n",
    "        alpha = annualized_return - (risk_free_rate + beta * (benchmark_annual_return - risk_free_rate))\n",
    "        \n",
    "        # Information ratio\n",
    "        excess_returns = returns - benchmark_returns\n",
    "        tracking_error = excess_returns.std() * np.sqrt(252)\n",
    "        information_ratio = excess_returns.mean() * np.sqrt(252) / tracking_error if tracking_error > 0 else 0\n",
    "        \n",
    "        # VaR and CVaR (95% confidence)\n",
    "        var_95 = np.percentile(returns, 5)\n",
    "        cvar_95 = returns[returns <= var_95].mean()\n",
    "        \n",
    "        self.performance_metrics = {\n",
    "            'total_return': total_return,\n",
    "            'annualized_return': annualized_return,\n",
    "            'volatility': volatility,\n",
    "            'sharpe_ratio': sharpe_ratio,\n",
    "            'sortino_ratio': sortino_ratio,\n",
    "            'calmar_ratio': calmar_ratio,\n",
    "            'max_drawdown': max_drawdown,\n",
    "            'beta': beta,\n",
    "            'alpha': alpha,\n",
    "            'information_ratio': information_ratio,\n",
    "            'var_95': var_95,\n",
    "            'cvar_95': cvar_95,\n",
    "            'win_rate': (returns > 0).mean(),\n",
    "            'best_day': returns.max(),\n",
    "            'worst_day': returns.min()\n",
    "        }\n",
    "        \n",
    "        return self.performance_metrics\n",
    "    \n",
    "    def plot_performance_analysis(self):\n",
    "        \"\"\"\n",
    "        Create comprehensive performance visualization\n",
    "        \"\"\"\n",
    "        if self.portfolio_returns is None:\n",
    "            raise ValueError(\"Portfolio returns not calculated.\")\n",
    "        \n",
    "        fig, axes = plt.subplots(3, 2, figsize=(18, 15))\n",
    "        \n",
    "        # 1. Cumulative returns vs benchmark\n",
    "        axes[0,0].plot(self.portfolio_returns['date'], \n",
    "                      (self.portfolio_returns['cumulative_return'] - 1) * 100, \n",
    "                      label='Portfolio', linewidth=2, color='blue')\n",
    "        axes[0,0].plot(self.benchmark_returns['date'], \n",
    "                      (self.benchmark_returns['cumulative_return'] - 1) * 100, \n",
    "                      label='VN-Index Benchmark', linewidth=2, color='red', alpha=0.7)\n",
    "        axes[0,0].set_title('Cumulative Returns: Portfolio vs Benchmark')\n",
    "        axes[0,0].set_ylabel('Cumulative Return (%)')\n",
    "        axes[0,0].legend()\n",
    "        axes[0,0].grid(True, alpha=0.3)\n",
    "        \n",
    "        # 2. Drawdown analysis\n",
    "        cumulative_returns = self.portfolio_returns['cumulative_return']\n",
    "        running_max = cumulative_returns.expanding().max()\n",
    "        drawdown = (cumulative_returns - running_max) / running_max * 100\n",
    "        \n",
    "        axes[0,1].fill_between(self.portfolio_returns['date'], drawdown, 0, \n",
    "                              alpha=0.3, color='red', label='Drawdown')\n",
    "        axes[0,1].plot(self.portfolio_returns['date'], drawdown, color='red', linewidth=1)\n",
    "        axes[0,1].set_title('Portfolio Drawdown Analysis')\n",
    "        axes[0,1].set_ylabel('Drawdown (%)')\n",
    "        axes[0,1].legend()\n",
    "        axes[0,1].grid(True, alpha=0.3)\n",
    "        \n",
    "        # 3. Rolling Sharpe ratio\n",
    "        rolling_sharpe = (self.portfolio_returns['daily_return'].rolling(60).mean() * 252) / \\\n",
    "                        (self.portfolio_returns['daily_return'].rolling(60).std() * np.sqrt(252))\n",
    "        \n",
    "        axes[1,0].plot(self.portfolio_returns['date'], rolling_sharpe, \n",
    "                      color='green', linewidth=1.5, label='60-day Rolling Sharpe')\n",
    "        axes[1,0].axhline(y=1, color='black', linestyle='--', alpha=0.5, label='Sharpe = 1')\n",
    "        axes[1,0].set_title('Rolling Sharpe Ratio (60-day)')\n",
    "        axes[1,0].set_ylabel('Sharpe Ratio')\n",
    "        axes[1,0].legend()\n",
    "        axes[1,0].grid(True, alpha=0.3)\n",
    "        \n",
    "        # 4. Return distribution\n",
    "        axes[1,1].hist(self.portfolio_returns['daily_return'] * 100, bins=50, \n",
    "                      alpha=0.7, color='skyblue', edgecolor='black')\n",
    "        axes[1,1].axvline(self.performance_metrics['var_95'] * 100, \n",
    "                         color='red', linestyle='--', label='VaR 95%')\n",
    "        axes[1,1].axvline(self.performance_metrics['cvar_95'] * 100, \n",
    "                         color='darkred', linestyle='--', label='CVaR 95%')\n",
    "        axes[1,1].set_title('Daily Return Distribution')\n",
    "        axes[1,1].set_xlabel('Daily Return (%)')\n",
    "        axes[1,1].set_ylabel('Frequency')\n",
    "        axes[1,1].legend()\n",
    "        axes[1,1].grid(True, alpha=0.3)\n",
    "        \n",
    "        # 5. Rolling volatility\n",
    "        rolling_vol = self.portfolio_returns['daily_return'].rolling(30).std() * np.sqrt(252) * 100\n",
    "        \n",
    "        axes[2,0].plot(self.portfolio_returns['date'], rolling_vol, \n",
    "                      color='orange', linewidth=1.5, label='30-day Rolling Volatility')\n",
    "        axes[2,0].set_title('Rolling Volatility (30-day)')\n",
    "        axes[2,0].set_ylabel('Volatility (%)')\n",
    "        axes[2,0].legend()\n",
    "        axes[2,0].grid(True, alpha=0.3)\n",
    "        \n",
    "        # 6. Performance metrics summary table\n",
    "        axes[2,1].axis('off')\n",
    "        metrics_text = f\"\"\"\n",
    "        PERFORMANCE METRICS SUMMARY\n",
    "        ═══════════════════════════════\n",
    "        \n",
    "        Returns:\n",
    "        • Total Return: {self.performance_metrics['total_return']:.1%}\n",
    "        • Annualized Return: {self.performance_metrics['annualized_return']:.1%}\n",
    "        \n",
    "        Risk Metrics:\n",
    "        • Volatility: {self.performance_metrics['volatility']:.1%}\n",
    "        • Max Drawdown: {self.performance_metrics['max_drawdown']:.1%}\n",
    "        • VaR (95%): {self.performance_metrics['var_95']:.2%}\n",
    "        \n",
    "        Risk-Adjusted Returns:\n",
    "        • Sharpe Ratio: {self.performance_metrics['sharpe_ratio']:.2f}\n",
    "        • Sortino Ratio: {self.performance_metrics['sortino_ratio']:.2f}\n",
    "        • Calmar Ratio: {self.performance_metrics['calmar_ratio']:.2f}\n",
    "        \n",
    "        Benchmark Comparison:\n",
    "        • Beta: {self.performance_metrics['beta']:.2f}\n",
    "        • Alpha: {self.performance_metrics['alpha']:.1%}\n",
    "        • Information Ratio: {self.performance_metrics['information_ratio']:.2f}\n",
    "        \n",
    "        Other:\n",
    "        • Win Rate: {self.performance_metrics['win_rate']:.1%}\n",
    "        • Best Day: {self.performance_metrics['best_day']:.2%}\n",
    "        • Worst Day: {self.performance_metrics['worst_day']:.2%}\n",
    "        \"\"\"\n",
    "        \n",
    "        axes[2,1].text(0.05, 0.95, metrics_text, transform=axes[2,1].transAxes, \n",
    "                      fontsize=10, verticalalignment='top', fontfamily='monospace',\n",
    "                      bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "        \n",
    "        return fig\n",
    "\n",
    "# Run portfolio performance analysis\n",
    "portfolio_analyzer = PortfolioPerformanceAnalyzer(optimal_allocation)\n",
    "portfolio_returns, benchmark_returns = portfolio_analyzer.simulate_portfolio_returns()\n",
    "performance_metrics = portfolio_analyzer.calculate_performance_metrics()\n",
    "\n",
    "print(\"\\nPORTFOLIO PERFORMANCE ANALYSIS\")\n",
    "print(\"=\" * 50)\n",
    "print(f\"Total Return: {performance_metrics['total_return']:.1%}\")\n",
    "print(f\"Annualized Return: {performance_metrics['annualized_return']:.1%}\")\n",
    "print(f\"Volatility: {performance_metrics['volatility']:.1%}\")\n",
    "print(f\"Sharpe Ratio: {performance_metrics['sharpe_ratio']:.2f}\")\n",
    "print(f\"Max Drawdown: {performance_metrics['max_drawdown']:.1%}\")\n",
    "print(f\"Alpha vs VN-Index: {performance_metrics['alpha']:.1%}\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}

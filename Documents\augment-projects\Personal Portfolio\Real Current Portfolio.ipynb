{"cells": [{"cell_type": "code", "execution_count": 1, "id": "311dc724", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: vnstock in c:\\users\\<USER>\\anaconda\\lib\\site-packages (3.2.6)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (2.31.0)\n", "Requirement already satisfied: beautifulsoup4 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (4.12.2)\n", "Requirement already satisfied: pandas in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (2.0.3)\n", "Requirement already satisfied: seaborn in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (0.12.2)\n", "Requirement already satisfied: openpyxl in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (3.0.10)\n", "Requirement already satisfied: pydantic in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (1.10.8)\n", "Requirement already satisfied: psutil in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (5.9.0)\n", "Requirement already satisfied: fake_useragent in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (2.2.0)\n", "Requirement already satisfied: vnstock_ezchart in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (0.0.2)\n", "Requirement already satisfied: click in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (8.0.4)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (23.1)\n", "Requirement already satisfied: importlib-metadata>=1.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (6.0.0)\n", "Requirement already satisfied: tenacity in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (8.2.2)\n", "Requirement already satisfied: vnai>=2.0.3 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (2.0.4)\n", "Requirement already satisfied: zipp>=0.5 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from importlib-metadata>=1.0->vnstock) (3.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from requests->vnstock) (2.0.4)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from requests->vnstock) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from requests->vnstock) (1.26.16)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from requests->vnstock) (2025.1.31)\n", "Requirement already satisfied: soupsieve>1.2 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from beautifulsoup4->vnstock) (2.4)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from click->vnstock) (0.4.6)\n", "Requirement already satisfied: et_xmlfile in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from openpyxl->vnstock) (1.1.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from pandas->vnstock) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from pandas->vnstock) (2023.3.post1)\n", "Requirement already satisfied: tzdata>=2022.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from pandas->vnstock) (2023.3)\n", "Requirement already satisfied: numpy>=1.21.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from pandas->vnstock) (1.24.3)\n", "Requirement already satisfied: typing-extensions>=4.2.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from pydantic->vnstock) (4.7.1)\n", "Requirement already satisfied: matplotlib!=3.6.1,>=3.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from seaborn->vnstock) (3.7.2)\n", "Requirement already satisfied: squarify in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock_ezchart->vnstock) (0.4.4)\n", "Requirement already satisfied: wordcloud in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock_ezchart->vnstock) (1.9.4)\n", "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (1.0.5)\n", "Requirement already satisfied: cycler>=0.10 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (0.11.0)\n", "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (4.25.0)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (1.4.4)\n", "Requirement already satisfied: pillow>=6.2.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (10.2.0)\n", "Requirement already satisfied: pyparsing<3.1,>=2.3.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (3.0.9)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from python-dateutil>=2.8.2->pandas->vnstock) (1.16.0)\n"]}], "source": ["!pip install vnstock\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Statistical analysis\n", "from scipy import stats\n", "from sklearn.preprocessing import StandardScaler\n", "import yfinance as yf"]}, {"cell_type": "code", "execution_count": 2, "id": "886b34de", "metadata": {}, "outputs": [{"data": {"text/html": ["<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "    <link href=\"https://fonts.googleapis.com/css2?family=Lexend:wght@400;500;600;700&display=swap\" rel=\"stylesheet\">\n", "    <style>\n", "        .vnstock-ad-banner {\n", "            all: initial;\n", "            font-family: '<PERSON><PERSON>', sans-serif;\n", "            display: flex;\n", "            flex-wrap: wrap;\n", "            max-width: 100%;\n", "            margin: 12px 0;\n", "            border-radius: 10px;\n", "            overflow: hidden;\n", "            background: #ffffff;\n", "            box-shadow: 0 4px 12px rgba(0,0,0,0.08);\n", "            color: #333;\n", "        }\n", "\n", "        .vnstock-ad-content {\n", "            flex: 1;\n", "            padding: 12px 16px;\n", "            display: flex;\n", "            flex-direction: column;\n", "            justify-content: center;\n", "        }\n", "\n", "        .vnstock-ad-title {\n", "            margin: 0 0 10px 0;\n", "            line-height: 1.3;\n", "            font-size: 18px;\n", "            font-weight: 700;\n", "            color: #4CAF50;\n", "        }\n", "\n", "        .title-highlight {\n", "            color: #8C52FF;\n", "        }\n", "\n", "        .vnstock-ad-features {\n", "            list-style: none;\n", "            padding-left: 0;\n", "            margin: 10px 0 12px 0;\n", "            font-size: 13px;\n", "            line-height: 1.4;\n", "        }\n", "\n", "        .vnstock-ad-features li {\n", "            margin-bottom: 6px;\n", "        }\n", "\n", "        .feature-highlight {\n", "            color: #8C52FF;\n", "            font-weight: 600;\n", "        }\n", "\n", "        .button-container {\n", "            text-align: center;\n", "        }\n", "\n", "        .vnstock-ad-button {\n", "            display: inline-block;\n", "            background-color: #4CAF50;\n", "            color: #fff;\n", "            padding: 6px 16px;\n", "            text-decoration: none;\n", "            font-size: 13px;\n", "            border-radius: 20px;\n", "            font-weight: 600;\n", "            transition: all 0.2s;\n", "            box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);\n", "        }\n", "\n", "        .vnstock-ad-button:hover {\n", "            background-color: #8C52FF;\n", "            color: white;\n", "            transform: translateY(-1px);\n", "            box-shadow: 0 4px 8px rgba(140, 82, 255, 0.3);\n", "        }\n", "\n", "        .vnstock-ad-image {\n", "            flex: 1;\n", "            min-width: 180px;\n", "            max-width: 45%;\n", "        }\n", "\n", "        .vnstock-ad-image img {\n", "            width: 100%;\n", "            height: 100%;\n", "            max-height: 250px;\n", "            object-fit: cover;\n", "            display: block;\n", "        }\n", "\n", "        @media (max-width: 768px) {\n", "            .vnstock-ad-banner {\n", "                flex-direction: column;\n", "            }\n", "            \n", "            .vnstock-ad-image {\n", "                min-width: auto;\n", "                max-width: 100%;\n", "            }\n", "        }\n", "    </style>\n", "</head>\n", "<body>\n", "    <div class=\"vnstock-ad-banner\">\n", "        <div class=\"vnstock-ad-content\">\n", "            <h2 class=\"vnstock-ad-title\">\n", "                <span class=\"title-highlight\">🤖 Python Coding với AI:</span> <PERSON>ân Tích Dữ liệu & <PERSON><PERSON> Chứng Khoán K11\n", "            </h2>\n", "            <ul class=\"vnstock-ad-features\">\n", "                <li><span class=\"feature-highlight\">⚡ Đi<PERSON>u k<PERSON>ển AI viết code Python:</span> <PERSON><PERSON><PERSON>ng cần thuộc lòng cú pháp, h<PERSON><PERSON> cách dùng AI agent</li>\n", "                <li><span class=\"feature-highlight\">🎯 Từ ý tưởng đến sản phẩm:</span> 15+ bot & chương trình thực tế hoàn chỉnh trong 10 buổi học</li>\n", "                <li><span class=\"feature-highlight\">🚀 Giáo trình mới tập trung vào AI:</span> <PERSON><PERSON> dụng cách tiếp cận AI thực chiến từ 15/6/2025. <PERSON><PERSON><PERSON> cập trước nội dung khóa 10 ngay khi đăng ký.</li>\n", "                <li><span class=\"feature-highlight\">💡 Cộng đồng chất lượng cao:</span> 100+ nhà đầu tư, chuyên gia & lập trình viên cùng học hỏi</li>\n", "            </ul>\n", "            <div class=\"button-container\">\n", "                <a href=\"https://vnstocks.com/lp-khoa-hoc-python-chung-khoan\" class=\"vnstock-ad-button\">\n", "                    <PERSON><PERSON>ng ký ngay ›\n", "                </a>\n", "            </div>\n", "        </div>\n", "        <a href=\"https://vnstocks.com/lp-khoa-hoc-python-chung-khoan\" class=\"vnstock-ad-image\">\n", "            <img src=\"https://vnstocks.com/img/cta-python-chung-khoan-k11-start-now.jpg\" alt=\"Khóa học Python Coding với AI\" style=\"width: 100%; height: 100%; max-height: 250px; object-fit: cover; display: block;\">\n", "        </a>\n", "    </div>\n", "</body>\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from vnstock import Listing, Quote, Company, Finance, Trading, Screener \n", "from vnstock.explorer.fmarket.fund import Fund\n", "from vnstock.explorer.misc import *"]}, {"cell_type": "code", "execution_count": 4, "id": "5e07520e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error processing E1VFN30: name 'vn' is not defined\n", "Error processing FIC: name 'vn' is not defined\n", "Error processing EIB: name 'vn' is not defined\n", "Error processing PPH: name 'vn' is not defined\n", "Error processing TV1: name 'vn' is not defined\n", "No valid data available for portfolio calculation\n"]}], "source": ["# Portfolio configuration\n", "portfolio_tickers = ['E1VFN30', 'FIC', 'EIB', 'PPH', 'TV1']\n", "total_portfolio_value = 129_966_634  # VND\n", "cash_pct = 97.7  # 97.7% in cash\n", "invested_pct = 100 - cash_pct  # 2.3% invested\n", "\n", "# Date range for historical analysis\n", "end_date = datetime.now()\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=5*365)\n", "start_date_str = start_date.strftime('%Y-%m-%d')\n", "end_date_str = end_date.strftime('%Y-%m-%d')\n", "\n", "# Fetch and process data\n", "returns_data = {}\n", "for symbol in portfolio_tickers:\n", "    try:\n", "        # Fetch historical data\n", "        df = vn.stock_historical_data(symbol, start_date_str, end_date_str, '1D', source='VCI')\n", "        df['time'] = pd.to_datetime(df['time'])\n", "        df = df.set_index('time').sort_index()\n", "        \n", "        # Calculate daily returns\n", "        returns = df['close'].pct_change().dropna()\n", "        returns_data[symbol] = returns\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {symbol}: {str(e)}\")\n", "        returns_data[symbol] = pd.Series(dtype=float)\n", "\n", "# Create returns matrix\n", "returns_df = pd.DataFrame(returns_data).dropna()\n", "\n", "if returns_df.empty:\n", "    print(\"No valid data available for portfolio calculation\")\n", "else:\n", "    # Portfolio weights (equal weighting for invested portion)\n", "    n_assets = len(returns_df.columns)\n", "    asset_weight = invested_pct / 100 / n_assets\n", "    \n", "    # Portfolio returns calculation\n", "    portfolio_daily_returns = returns_df.mean(axis=1) * (invested_pct/100)\n", "    \n", "    # Portfolio value simulation\n", "    initial_investment = total_portfolio_value\n", "    portfolio_values = [initial_investment]\n", "    \n", "    for r in portfolio_daily_returns:\n", "        new_value = portfolio_values[-1] * (1 + r)\n", "        portfolio_values.append(new_value)\n", "    \n", "    portfolio_values = pd.Series(portfolio_values[1:], index=returns_df.index)\n", "    \n", "    # Calculate metrics (portfolio level)\n", "    # Annualized return\n", "    annual_return = (1 + portfolio_daily_returns.mean())**252 - 1\n", "    \n", "    # Annualized volatility\n", "    annual_vol = portfolio_daily_returns.std() * np.sqrt(252)\n", "    \n", "    # <PERSON>io (risk-free rate = 0)\n", "    sharpe_ratio = annual_return / annual_vol if annual_vol > 0 else 0\n", "    \n", "    # Max Drawdown\n", "    rolling_max = portfolio_values.cummax()\n", "    drawdowns = (portfolio_values - rolling_max) / rolling_max\n", "    max_drawdown = drawdowns.min()\n", "    \n", "    # Value at Risk (95% and 99%)\n", "    var_95 = np.percentile(portfolio_daily_returns, 5)\n", "    var_99 = np.percentile(portfolio_daily_returns, 1)\n", "\n", "    # Create portfolio metrics summary\n", "    portfolio_metrics = {\n", "        'Portfolio Return (%)': annual_return * 100,\n", "        'Portfolio Volatility (%)': annual_vol * 100,\n", "        'Sharpe Ratio': sharpe_ratio,\n", "        'Max Drawdown (%)': max_drawdown * 100,\n", "        '95% VaR (Daily Loss)': var_95 * 100,\n", "        '99% VaR (Daily Loss)': var_99 * 100,\n", "        'Cash Allocation (%)': cash_pct,\n", "        'Invested Assets (%)': invested_pct,\n", "        'Analysis Period': f\"{start_date_str} to {end_date_str}\",\n", "        'Valid Assets Used': len(returns_df.columns)\n", "    }\n", "    \n", "    # Display results\n", "    print(\"\\nPortfolio Risk-Return Analysis:\")\n", "    print(\"=\" * 50)\n", "    for metric, value in portfolio_metrics.items():\n", "        if 'Ratio' in metric:\n", "            print(f\"{metric}: {value:.4f}\")\n", "        elif 'Allocation' in metric or 'Period' in metric or 'Assets' in metric:\n", "            print(f\"{metric}: {value}\")\n", "        else:\n", "            print(f\"{metric}: {value:.2f}%\")\n", "\n", "    # Plot portfolio value simulation\n", "    import matplotlib.pyplot as plt\n", "    plt.figure(figsize=(12, 6))\n", "    portfolio_values.plot(title='Portfolio Value Simulation')\n", "    plt.ylabel('Portfolio Value (VND)')\n", "    plt.xlabel('Date')\n", "    plt.grid(True)\n", "    plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
# Uncomment and run this cell to install vnstock
# !pip install vnstock

# Also install other required packages if needed
# !pip install yfinance pandas numpy matplotlib seaborn scikit-learn scipy

print("Installation commands ready. Uncomment the lines above to install.")

# Core libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Statistical analysis
from scipy import stats
from sklearn.preprocessing import StandardScaler

# Financial data
import yfinance as yf

# VNSTOCK - Real Vietnamese Market Data
try:
    import vnstock
    from vnstock import Vnstock, Listing, Quote, Company, Finance, Trading, Screener
    print("vnstock imported successfully! Real Vietnamese market data available.")
    print(f"vnstock version: {vnstock.__version__ if hasattr(vnstock, '__version__') else 'Unknown'}")
    VNSTOCK_AVAILABLE = True
except ImportError:
    print("vnstock not found. Install with: pip install vnstock")
    print("Using simulated data for demonstration.")
    VNSTOCK_AVAILABLE = False

plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("Vietnamese Market Portfolio Optimizer Ready!")
print(f"Real data available: {'YES' if VNSTOCK_AVAILABLE else 'NO (using simulated data)'}")

class VnstockMarketData:
    def __init__(self):
        self.stocks_data = None
        self.currency_data = None
        self.gold_data = None
        self.vnindex_data = None
        self.vnstock_available = VNSTOCK_AVAILABLE
    
    def load_vietnamese_stocks(self, limit=50, exchanges=['HOSE', 'HNX']):
        """
        Load real Vietnamese stock data using vnstock
        """
        if not self.vnstock_available:
            print("vnstock not available.")
            return None
        
        try:
            print("Fetching Vietnamese stock data with vnstock...")
            
            # Get all listed symbols
            listing = Listing()
            all_symbols = listing.all_symbols()
            
            # Filter for specified exchanges
            main_stocks = all_symbols[
                all_symbols['exchange'].isin(exchanges)
            ].head(limit)
            
            print(f"Found {len(main_stocks)} stocks on {exchanges} exchanges")
            
            # Collect stock data
            stocks_list = []
            successful_count = 0
            
            for idx, row in main_stocks.iterrows():
                try:
                    symbol = row['symbol']
                    
                    # Get recent price data (last 6 months)
                    quote = Quote(symbol=symbol, source='VCI')
                    end_date = datetime.now().strftime('%Y-%m-%d')
                    start_date = (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d')
                    
                    price_data = quote.history(start=start_date, end=end_date, interval='1D')
                    
                    if not price_data.empty and len(price_data) > 10:
                        # Calculate metrics from real data
                        latest_price = price_data['close'].iloc[-1]
                        avg_volume = price_data['volume'].mean()
                        price_volatility = price_data['close'].pct_change().std() * np.sqrt(252)
                        
                        # Try to get company fundamentals
                        try:
                            company = Company(symbol=symbol, source='VCI')
                            overview = company.overview()
                            
                            # Extract real financial metrics if available
                            if isinstance(overview, pd.DataFrame) and not overview.empty:
                                market_cap = overview.get('marketCap', [np.nan])[0] if 'marketCap' in overview.columns else np.nan
                                pe_ratio = overview.get('pe', [np.nan])[0] if 'pe' in overview.columns else np.nan
                            else:
                                market_cap = np.nan
                                pe_ratio = np.nan
                                
                        except Exception:
                            market_cap = np.nan
                            pe_ratio = np.nan
                        
                        # Use reasonable defaults for missing data
                        if pd.isna(market_cap):
                            market_cap = latest_price * avg_volume * 50  # Rough estimate
                        if pd.isna(pe_ratio) or pe_ratio <= 0:
                            pe_ratio = np.random.uniform(8, 25)  # Reasonable range
                        
                        stock_info = {
                            'symbol': symbol,
                            'exchange': row['exchange'],
                            'price': latest_price,
                            'volume': avg_volume,
                            'market_cap': market_cap,
                            'pe_ratio': pe_ratio,
                            'volatility': price_volatility,
                            # Simulated fundamental metrics (would need Finance module for real data)
                            'debt_to_equity': np.random.uniform(0.1, 1.2),
                            'roe': np.random.uniform(-0.05, 0.25),
                            'revenue_growth': np.random.uniform(-0.15, 0.20)
                        }
                        
                        stocks_list.append(stock_info)
                        successful_count += 1
                        
                        if successful_count % 5 == 0:
                            print(f"Processed {successful_count} stocks...")
                            
                except Exception as e:
                    # Skip problematic stocks
                    continue
            
            if stocks_list:
                self.stocks_data = pd.DataFrame(stocks_list)
                print(f"Successfully loaded {len(self.stocks_data)} Vietnamese stocks with real market data!")
                print(f"Exchanges covered: {self.stocks_data['exchange'].value_counts().to_dict()}")
            else:
                print("No stock data retrieved.")
                return None
                
        except Exception as e:
            print(f"Error fetching vnstock data: {e}")
            return None
        
        return self.stocks_data

# Initialize the enhanced data loader
market_data = VnstockMarketData()
print("VnstockMarketData initialized!")

# Load real Vietnamese stock data
print("Loading Vietnamese stock data...")
stocks_df = market_data.load_vietnamese_stocks(limit=30, exchanges=['HOSE', 'HNX'])

if stocks_df is not None:
    # Display summary
    print(f"\nLOADED DATA SUMMARY:")
    print(f"Total stocks: {len(stocks_df)}")
    print(f"Exchanges: {stocks_df['exchange'].value_counts().to_dict()}")
    print(f"Price range: {stocks_df['price'].min():,.0f} - {stocks_df['price'].max():,.0f} VND")
    print(f"Average volume: {stocks_df['volume'].mean():,.0f}")
    
    # Show sample of the data
    print("\nSAMPLE DATA:")
    display(stocks_df[['symbol', 'exchange', 'price', 'volume', 'market_cap', 'pe_ratio']].head(10))
else:
    print("Failed to load stock data. Check vnstock installation.")

class EnhancedStockScreener:
    def __init__(self, stocks_data):
        self.stocks_data = stocks_data
    
    def screen_short_candidates(self, 
                              volume_percentile=25,     # Bottom 25% by volume
                              pe_threshold=25,          # High P/E ratios
                              debt_threshold=0.8,       # High debt
                              roe_threshold=0.06,       # Low ROE
                              revenue_growth_threshold=-0.05,  # Negative growth
                              volatility_threshold=0.35):      # High volatility
        """
        Enhanced screening for short candidates using real market data
        """
        df = self.stocks_data.copy()
        
        # Calculate thresholds
        volume_threshold = df['volume'].quantile(volume_percentile/100)
        
        # Apply screening criteria
        short_candidates = df[
            (df['volume'] <= volume_threshold) &  # Low volume
            (
                (df['pe_ratio'] > pe_threshold) |  # High P/E OR
                (df['debt_to_equity'] > debt_threshold) |  # High debt OR
                (df['roe'] < roe_threshold) |  # Low ROE OR
                (df['revenue_growth'] < revenue_growth_threshold) |  # Negative growth OR
                (df['volatility'] > volatility_threshold)  # High volatility
            )
        ]
        
        if len(short_candidates) == 0:
            print("No stocks meet the strict short criteria. Relaxing constraints...")
            # Relax criteria if no candidates found
            short_candidates = df[
                (df['volume'] <= df['volume'].quantile(0.4)) &
                (
                    (df['pe_ratio'] > 20) |
                    (df['debt_to_equity'] > 0.6) |
                    (df['roe'] < 0.08)
                )
            ]
        
        # Calculate enhanced "badness score"
        if len(short_candidates) > 0:
            # Normalize metrics for scoring
            pe_score = (short_candidates['pe_ratio'] / df['pe_ratio'].median()).fillna(1)
            debt_score = (short_candidates['debt_to_equity'] / df['debt_to_equity'].median()).fillna(1)
            roe_score = (1 / (short_candidates['roe'] + 0.01)).fillna(10)  # Inverse ROE
            volume_score = (1 / (short_candidates['volume'] / df['volume'].median() + 0.1)).fillna(5)
            volatility_score = (short_candidates['volatility'] / df['volatility'].median()).fillna(1)
            
            short_candidates['badness_score'] = (
                pe_score * 0.25 +
                debt_score * 0.25 +
                roe_score * 0.20 +
                volume_score * 0.15 +
                volatility_score * 0.15
            )
            
            # Add risk rating
            short_candidates['risk_rating'] = pd.cut(
                short_candidates['badness_score'], 
                bins=[0, 2, 4, 6, float('inf')], 
                labels=['Low', 'Medium', 'High', 'Extreme']
            )
        
        return short_candidates.sort_values('badness_score', ascending=False)
    
    def analyze_short_portfolio(self, short_candidates, max_positions=5):
        """
        Analyze potential short portfolio
        """
        if len(short_candidates) == 0:
            return None
            
        top_shorts = short_candidates.head(max_positions)
        
        portfolio_analysis = {
            'total_positions': len(top_shorts),
            'avg_badness_score': top_shorts['badness_score'].mean(),
            'avg_pe_ratio': top_shorts['pe_ratio'].mean(),
            'avg_debt_ratio': top_shorts['debt_to_equity'].mean(),
            'avg_roe': top_shorts['roe'].mean(),
            'avg_volatility': top_shorts['volatility'].mean(),
            'total_market_cap': top_shorts['market_cap'].sum(),
            'exchange_distribution': top_shorts['exchange'].value_counts().to_dict()
        }
        
        return portfolio_analysis

# Run enhanced screening only if we have data
if stocks_df is not None:
    print("Running enhanced stock screening...")
    screener = EnhancedStockScreener(stocks_df)
    short_candidates = screener.screen_short_candidates()
    
    print(f"\nSCREENING RESULTS:")
    print(f"Short candidates found: {len(short_candidates)}")
    
    if len(short_candidates) > 0:
        print("\nTOP SHORT CANDIDATES:")
        display(short_candidates[['symbol', 'exchange', 'price', 'volume', 'pe_ratio', 
                                 'debt_to_equity', 'roe', 'volatility', 'badness_score', 'risk_rating']].head())
        
        # Analyze short portfolio
        portfolio_analysis = screener.analyze_short_portfolio(short_candidates)
        print(f"\nSHORT PORTFOLIO ANALYSIS:")
        for key, value in portfolio_analysis.items():
            if isinstance(value, float):
                print(f"{key}: {value:.3f}")
            else:
                print(f"{key}: {value}")
    else:
        print("No short candidates found with current criteria.")
else:
    print("Cannot run screening without stock data.")

class ShortStrategyAnalyzer:
    def __init__(self):
        self.vnindex_data = None
        self.pnj_data = None
        self.analysis_results = {}
    
    def fetch_historical_data(self, years_back=6):
        """
        Fetch historical data for VN-Index and PNJ
        """
        if not VNSTOCK_AVAILABLE:
            print("vnstock not available for historical analysis.")
            return False
        
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=years_back*365)).strftime('%Y-%m-%d')
            
            print(f"Fetching historical data from {start_date} to {end_date}...")
            
            # Fetch VN-Index data
            try:
                vnindex = Vnstock().stock(symbol='VNINDEX', source='VCI')
                self.vnindex_data = vnindex.quote.history(start=start_date, end=end_date, interval='1D')
                if not self.vnindex_data.empty:
                    self.vnindex_data = self.vnindex_data.reset_index()
                    print(f"VN-Index: {len(self.vnindex_data)} days of data loaded")
                else:
                    print("No VN-Index data retrieved")
            except Exception as e:
                print(f"Error fetching VN-Index data: {e}")
            
            # Fetch PNJ data
            try:
                pnj_quote = Quote(symbol='PNJ', source='VCI')
                self.pnj_data = pnj_quote.history(start=start_date, end=end_date, interval='1D')
                if not self.pnj_data.empty:
                    self.pnj_data = self.pnj_data.reset_index()
                    print(f"PNJ: {len(self.pnj_data)} days of data loaded")
                else:
                    print("No PNJ data retrieved")
            except Exception as e:
                print(f"Error fetching PNJ data: {e}")
            
            return (self.vnindex_data is not None and not self.vnindex_data.empty) or \
                   (self.pnj_data is not None and not self.pnj_data.empty)
                   
        except Exception as e:
            print(f"Error in data fetching: {e}")
            return False
    
    def calculate_short_performance(self, data, symbol_name):
        """
        Calculate performance metrics for shorting a stock/index
        """
        if data is None or data.empty:
            return None
        
        # Calculate daily returns
        data['daily_return'] = data['close'].pct_change()
        
        # Short returns are negative of long returns
        data['short_return'] = -data['daily_return']
        
        # Remove NaN values
        short_returns = data['short_return'].dropna()
        
        if len(short_returns) == 0:
            return None
        
        # Calculate performance metrics
        total_return = (1 + short_returns).prod() - 1
        annualized_return = (1 + total_return) ** (252 / len(short_returns)) - 1
        volatility = short_returns.std() * np.sqrt(252)
        
        # Sharpe ratio (assuming 3% risk-free rate)
        risk_free_rate = 0.03
        sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # Win rate and average win/loss
        winning_days = short_returns[short_returns > 0]
        losing_days = short_returns[short_returns < 0]
        
        win_rate = len(winning_days) / len(short_returns)
        avg_win = winning_days.mean() if len(winning_days) > 0 else 0
        avg_loss = losing_days.mean() if len(losing_days) > 0 else 0
        
        # Expected Value calculation
        expected_value = (win_rate * avg_win) + ((1 - win_rate) * avg_loss)
        
        # Maximum drawdown
        cumulative_returns = (1 + short_returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Calmar ratio
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Value at Risk (95%)
        var_95 = np.percentile(short_returns, 5)
        
        metrics = {
            'symbol': symbol_name,
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'calmar_ratio': calmar_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'expected_value_daily': expected_value,
            'expected_value_annual': expected_value * 252,
            'var_95': var_95,
            'total_trading_days': len(short_returns),
            'data_start': data['time'].min() if 'time' in data.columns else 'Unknown',
            'data_end': data['time'].max() if 'time' in data.columns else 'Unknown'
        }
        
        return metrics
    
    def analyze_short_strategies(self):
        """
        Analyze short strategies for both VN-Index and PNJ
        """
        results = {}
        
        # Analyze VN-Index short
        if self.vnindex_data is not None and not self.vnindex_data.empty:
            print("\nAnalyzing VN-Index short strategy...")
            vnindex_metrics = self.calculate_short_performance(self.vnindex_data, 'VN-Index')
            if vnindex_metrics:
                results['VNINDEX'] = vnindex_metrics
        
        # Analyze PNJ short
        if self.pnj_data is not None and not self.pnj_data.empty:
            print("Analyzing PNJ short strategy...")
            pnj_metrics = self.calculate_short_performance(self.pnj_data, 'PNJ')
            if pnj_metrics:
                results['PNJ'] = pnj_metrics
        
        self.analysis_results = results
        return results
    
    def display_results(self):
        """
        Display comprehensive analysis results
        """
        if not self.analysis_results:
            print("No analysis results available.")
            return
        
        print("\n" + "="*80)
        print("SHORT STRATEGY PERFORMANCE ANALYSIS")
        print("="*80)
        
        for symbol, metrics in self.analysis_results.items():
            print(f"\n{symbol} SHORT STRATEGY:")
            print("-" * 40)
            print(f"Data Period: {metrics['data_start']} to {metrics['data_end']}")
            print(f"Trading Days: {metrics['total_trading_days']}")
            print(f"\nRETURNS:")
            print(f"  Total Return: {metrics['total_return']:.2%}")
            print(f"  Annualized Return: {metrics['annualized_return']:.2%}")
            print(f"  Expected Value (Daily): {metrics['expected_value_daily']:.4f}")
            print(f"  Expected Value (Annual): {metrics['expected_value_annual']:.2%}")
            print(f"\nRISK METRICS:")
            print(f"  Volatility: {metrics['volatility']:.2%}")
            print(f"  Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
            print(f"  Calmar Ratio: {metrics['calmar_ratio']:.3f}")
            print(f"  Max Drawdown: {metrics['max_drawdown']:.2%}")
            print(f"  VaR (95%): {metrics['var_95']:.4f}")
            print(f"\nTRADING STATISTICS:")
            print(f"  Win Rate: {metrics['win_rate']:.2%}")
            print(f"  Average Win: {metrics['avg_win']:.4f}")
            print(f"  Average Loss: {metrics['avg_loss']:.4f}")
            print(f"  Win/Loss Ratio: {abs(metrics['avg_win']/metrics['avg_loss']):.2f}" if metrics['avg_loss'] != 0 else "  Win/Loss Ratio: N/A")
        
        # Summary comparison
        if len(self.analysis_results) > 1:
            print(f"\nSTRATEGY COMPARISON:")
            print("-" * 40)
            comparison_df = pd.DataFrame(self.analysis_results).T
            key_metrics = ['annualized_return', 'sharpe_ratio', 'max_drawdown', 'win_rate', 'expected_value_annual']
            display(comparison_df[key_metrics].round(4))
    
    def plot_performance(self):
        """
        Plot cumulative performance of short strategies
        """
        if not self.analysis_results:
            print("No data to plot.")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        plot_data = {}
        
        # Prepare data for plotting
        if 'VNINDEX' in self.analysis_results and self.vnindex_data is not None:
            vnindex_short_returns = -self.vnindex_data['close'].pct_change().dropna()
            vnindex_cumulative = (1 + vnindex_short_returns).cumprod()
            plot_data['VN-Index Short'] = vnindex_cumulative
        
        if 'PNJ' in self.analysis_results and self.pnj_data is not None:
            pnj_short_returns = -self.pnj_data['close'].pct_change().dropna()
            pnj_cumulative = (1 + pnj_short_returns).cumprod()
            plot_data['PNJ Short'] = pnj_cumulative
        
        if plot_data:
            # Plot 1: Cumulative returns
            for name, data in plot_data.items():
                axes[0,0].plot(data.index, (data - 1) * 100, label=name, linewidth=2)
            axes[0,0].set_title('Cumulative Returns - Short Strategies')
            axes[0,0].set_ylabel('Cumulative Return (%)')
            axes[0,0].legend()
            axes[0,0].grid(True, alpha=0.3)
            
            # Plot 2: Sharpe ratios comparison
            symbols = list(self.analysis_results.keys())
            sharpe_ratios = [self.analysis_results[s]['sharpe_ratio'] for s in symbols]
            colors = ['red' if x < 0 else 'green' for x in sharpe_ratios]
            axes[0,1].bar(symbols, sharpe_ratios, color=colors, alpha=0.7)
            axes[0,1].set_title('Sharpe Ratios - Short Strategies')
            axes[0,1].set_ylabel('Sharpe Ratio')
            axes[0,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
            axes[0,1].grid(True, alpha=0.3)
            
            # Plot 3: Expected Value comparison
            expected_values = [self.analysis_results[s]['expected_value_annual'] * 100 for s in symbols]
            colors = ['red' if x < 0 else 'green' for x in expected_values]
            axes[1,0].bar(symbols, expected_values, color=colors, alpha=0.7)
            axes[1,0].set_title('Expected Value (Annual) - Short Strategies')
            axes[1,0].set_ylabel('Expected Value (%)')
            axes[1,0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
            axes[1,0].grid(True, alpha=0.3)
            
            # Plot 4: Risk-Return scatter
            returns = [self.analysis_results[s]['annualized_return'] * 100 for s in symbols]
            volatilities = [self.analysis_results[s]['volatility'] * 100 for s in symbols]
            axes[1,1].scatter(volatilities, returns, s=100, alpha=0.7)
            for i, symbol in enumerate(symbols):
                axes[1,1].annotate(symbol, (volatilities[i], returns[i]), 
                                 xytext=(5, 5), textcoords='offset points')
            axes[1,1].set_title('Risk-Return Profile - Short Strategies')
            axes[1,1].set_xlabel('Volatility (%)')
            axes[1,1].set_ylabel('Annualized Return (%)')
            axes[1,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
            axes[1,1].axvline(x=0, color='black', linestyle='--', alpha=0.5)
            axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

# Run the short strategy analysis
print("ANALYZING SHORT STRATEGIES FOR VN-INDEX AND PNJ")
print("=" * 50)

analyzer = ShortStrategyAnalyzer()

# Fetch historical data
if analyzer.fetch_historical_data(years_back=6):
    # Analyze strategies
    results = analyzer.analyze_short_strategies()
    
    if results:
        # Display results
        analyzer.display_results()
        
        # Plot performance
        analyzer.plot_performance()
        
        print("\nSTRATEGY RECOMMENDATIONS:")
        print("-" * 30)
        for symbol, metrics in results.items():
            if metrics['sharpe_ratio'] > 0.5:
                print(f"{symbol}: STRONG short candidate (Sharpe: {metrics['sharpe_ratio']:.2f})")
            elif metrics['sharpe_ratio'] > 0:
                print(f"{symbol}: MODERATE short candidate (Sharpe: {metrics['sharpe_ratio']:.2f})")
            else:
                print(f"{symbol}: POOR short candidate (Sharpe: {metrics['sharpe_ratio']:.2f})")
    else:
        print("No analysis results generated.")
else:
    print("Failed to fetch historical data for analysis.")
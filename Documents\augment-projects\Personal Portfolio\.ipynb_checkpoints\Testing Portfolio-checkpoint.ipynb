{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2c7abaa1", "metadata": {}, "outputs": [], "source": ["# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Statistical analysis\n", "from scipy import stats\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "# Financial data\n", "import yfinance as yf"]}, {"cell_type": "code", "execution_count": 3, "id": "68ce38a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting vnstock\n", "  Obtaining dependency information for vnstock from https://files.pythonhosted.org/packages/86/15/24f2e70957cf3753471685df36340cf0b5edc83b22d077642639697b1e94/vnstock-3.2.6-py3-none-any.whl.metadata\n", "  Downloading vnstock-3.2.6-py3-none-any.whl.metadata (42 kB)\n", "     ---------------------------------------- 0.0/42.5 kB ? eta -:--:--\n", "     ------------------ ------------------- 20.5/42.5 kB 330.3 kB/s eta 0:00:01\n", "     -------------------------------------- 42.5/42.5 kB 342.7 kB/s eta 0:00:00\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (2.31.0)\n", "Requirement already satisfied: beautifulsoup4 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (4.12.2)\n", "Requirement already satisfied: pandas in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (2.0.3)\n", "Requirement already satisfied: seaborn in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (0.12.2)\n", "Requirement already satisfied: openpyxl in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (3.0.10)\n", "Requirement already satisfied: pydantic in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (1.10.8)\n", "Requirement already satisfied: psutil in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (5.9.0)\n", "Collecting fake_useragent (from vnstock)\n", "  Obtaining dependency information for fake_useragent from https://files.pythonhosted.org/packages/51/37/b3ea9cd5558ff4cb51957caca2193981c6b0ff30bd0d2630ac62505d99d0/fake_useragent-2.2.0-py3-none-any.whl.metadata\n", "  Downloading fake_useragent-2.2.0-py3-none-any.whl.metadata (17 kB)\n", "Collecting vnstock_ezchart (from vnstock)\n", "  Obtaining dependency information for vnstock_ezchart from https://files.pythonhosted.org/packages/c0/10/aa8ced9a95fd7610cf8540026709a42c389e75e98c5ad1d6cde820e02176/vnstock_ezchart-0.0.2-py3-none-any.whl.metadata\n", "  Downloading vnstock_ezchart-0.0.2-py3-none-any.whl.metadata (6.6 kB)\n", "Requirement already satisfied: click in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (8.0.4)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (23.1)\n", "Requirement already satisfied: importlib-metadata>=1.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (6.0.0)\n", "Requirement already satisfied: tenacity in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from vnstock) (8.2.2)\n", "Collecting vnai>=2.0.3 (from vnstock)\n", "  Obtaining dependency information for vnai>=2.0.3 from https://files.pythonhosted.org/packages/a3/d9/3612e56e7c91d4b115c90fe11ed64f8abe1e53caa1f1b9eb9f93e5085ce4/vnai-2.0.4-py3-none-any.whl.metadata\n", "  Downloading vnai-2.0.4-py3-none-any.whl.metadata (988 bytes)\n", "Requirement already satisfied: zipp>=0.5 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from importlib-metadata>=1.0->vnstock) (3.11.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from requests->vnstock) (2.0.4)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from requests->vnstock) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from requests->vnstock) (1.26.16)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from requests->vnstock) (2025.1.31)\n", "Requirement already satisfied: soupsieve>1.2 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from beautifulsoup4->vnstock) (2.4)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from click->vnstock) (0.4.6)\n", "Requirement already satisfied: et_xmlfile in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from openpyxl->vnstock) (1.1.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from pandas->vnstock) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from pandas->vnstock) (2023.3.post1)\n", "Requirement already satisfied: tzdata>=2022.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from pandas->vnstock) (2023.3)\n", "Requirement already satisfied: numpy>=1.21.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from pandas->vnstock) (1.24.3)\n", "Requirement already satisfied: typing-extensions>=4.2.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from pydantic->vnstock) (4.7.1)\n", "Requirement already satisfied: matplotlib!=3.6.1,>=3.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from seaborn->vnstock) (3.7.2)\n", "Collecting squarify (from vnstock_ezchart->vnstock)\n", "  Obtaining dependency information for squarify from https://files.pythonhosted.org/packages/b7/3c/eedbe9fb07cc20fd9a8423da14b03bc270d0570b3ba9174a4497156a2152/squarify-0.4.4-py3-none-any.whl.metadata\n", "  Downloading squarify-0.4.4-py3-none-any.whl.metadata (600 bytes)\n", "Collecting wordcloud (from vnstock_ezchart->vnstock)\n", "  Obtaining dependency information for wordcloud from https://files.pythonhosted.org/packages/00/09/abb305dce85911b8fba382926cfc57f2f257729e25937fdcc63f3a1a67f9/wordcloud-1.9.4-cp311-cp311-win_amd64.whl.metadata\n", "  Downloading wordcloud-1.9.4-cp311-cp311-win_amd64.whl.metadata (3.5 kB)\n", "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (1.0.5)\n", "Requirement already satisfied: cycler>=0.10 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (0.11.0)\n", "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (4.25.0)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (1.4.4)\n", "Requirement already satisfied: pillow>=6.2.0 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (10.2.0)\n", "Requirement already satisfied: pyparsing<3.1,>=2.3.1 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from matplotlib!=3.6.1,>=3.1->seaborn->vnstock) (3.0.9)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\anaconda\\lib\\site-packages (from python-dateutil>=2.8.2->pandas->vnstock) (1.16.0)\n", "Downloading vnstock-3.2.6-py3-none-any.whl (121 kB)\n", "   ---------------------------------------- 0.0/121.8 kB ? eta -:--:--\n", "   ------------- ------------------------- 41.0/121.8 kB 991.0 kB/s eta 0:00:01\n", "   ------------------------------ --------- 92.2/121.8 kB 1.3 MB/s eta 0:00:01\n", "   ------------------------------------ --- 112.6/121.8 kB 1.1 MB/s eta 0:00:01\n", "   -------------------------------------- 121.8/121.8 kB 892.9 kB/s eta 0:00:00\n", "Downloading vnai-2.0.4-py3-none-any.whl (32 kB)\n", "Downloading fake_useragent-2.2.0-py3-none-any.whl (161 kB)\n", "   ---------------------------------------- 0.0/161.7 kB ? eta -:--:--\n", "   ----------------------------------- ---- 143.4/161.7 kB 4.3 MB/s eta 0:00:01\n", "   ---------------------------------------- 161.7/161.7 kB 1.9 MB/s eta 0:00:00\n", "Downloading vnstock_ezchart-0.0.2-py3-none-any.whl (14 kB)\n", "Downloading squarify-0.4.4-py3-none-any.whl (4.1 kB)\n", "Downloading wordcloud-1.9.4-cp311-cp311-win_amd64.whl (299 kB)\n", "   ---------------------------------------- 0.0/299.9 kB ? eta -:--:--\n", "   ------------------------------------ --- 276.5/299.9 kB 8.6 MB/s eta 0:00:01\n", "   ---------------------------------------- 299.9/299.9 kB 3.7 MB/s eta 0:00:00\n", "Installing collected packages: squarify, fake_useragent, vnai, wordcloud, vnstock_ezchart, vnstock\n", "Successfully installed fake_useragent-2.2.0 squarify-0.4.4 vnai-2.0.4 vnstock-3.2.6 vnstock_ezchart-0.0.2 wordcloud-1.9.4\n"]}], "source": ["!pip install vnstock"]}, {"cell_type": "code", "execution_count": 4, "id": "41204539", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "    <link href=\"https://fonts.googleapis.com/css2?family=Lexend:wght@400;500;600;700&display=swap\" rel=\"stylesheet\">\n", "    <style>\n", "        .vnstock-ad-banner {\n", "            all: initial;\n", "            font-family: '<PERSON><PERSON>', sans-serif;\n", "            display: flex;\n", "            flex-wrap: wrap;\n", "            max-width: 100%;\n", "            margin: 12px 0;\n", "            border-radius: 10px;\n", "            overflow: hidden;\n", "            background: #ffffff;\n", "            box-shadow: 0 4px 12px rgba(0,0,0,0.08);\n", "            color: #333;\n", "        }\n", "\n", "        .vnstock-ad-content {\n", "            flex: 1;\n", "            padding: 12px 16px;\n", "            display: flex;\n", "            flex-direction: column;\n", "            justify-content: center;\n", "        }\n", "\n", "        .vnstock-ad-title {\n", "            margin: 0 0 10px 0;\n", "            line-height: 1.3;\n", "            font-size: 18px;\n", "            font-weight: 700;\n", "            color: #4CAF50;\n", "        }\n", "\n", "        .title-highlight {\n", "            color: #8C52FF;\n", "        }\n", "\n", "        .vnstock-ad-features {\n", "            list-style: none;\n", "            padding-left: 0;\n", "            margin: 10px 0 12px 0;\n", "            font-size: 13px;\n", "            line-height: 1.4;\n", "        }\n", "\n", "        .vnstock-ad-features li {\n", "            margin-bottom: 6px;\n", "        }\n", "\n", "        .feature-highlight {\n", "            color: #8C52FF;\n", "            font-weight: 600;\n", "        }\n", "\n", "        .button-container {\n", "            text-align: center;\n", "        }\n", "\n", "        .vnstock-ad-button {\n", "            display: inline-block;\n", "            background-color: #4CAF50;\n", "            color: #fff;\n", "            padding: 6px 16px;\n", "            text-decoration: none;\n", "            font-size: 13px;\n", "            border-radius: 20px;\n", "            font-weight: 600;\n", "            transition: all 0.2s;\n", "            box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);\n", "        }\n", "\n", "        .vnstock-ad-button:hover {\n", "            background-color: #8C52FF;\n", "            color: white;\n", "            transform: translateY(-1px);\n", "            box-shadow: 0 4px 8px rgba(140, 82, 255, 0.3);\n", "        }\n", "\n", "        .vnstock-ad-image {\n", "            flex: 1;\n", "            min-width: 180px;\n", "            max-width: 45%;\n", "        }\n", "\n", "        .vnstock-ad-image img {\n", "            width: 100%;\n", "            height: 100%;\n", "            max-height: 250px;\n", "            object-fit: cover;\n", "            display: block;\n", "        }\n", "\n", "        @media (max-width: 768px) {\n", "            .vnstock-ad-banner {\n", "                flex-direction: column;\n", "            }\n", "            \n", "            .vnstock-ad-image {\n", "                min-width: auto;\n", "                max-width: 100%;\n", "            }\n", "        }\n", "    </style>\n", "</head>\n", "<body>\n", "    <div class=\"vnstock-ad-banner\">\n", "        <div class=\"vnstock-ad-content\">\n", "            <h2 class=\"vnstock-ad-title\">\n", "                <span class=\"title-highlight\">🤖 Python Coding với AI:</span> <PERSON>ân Tích Dữ liệu & <PERSON><PERSON> Chứng Khoán K11\n", "            </h2>\n", "            <ul class=\"vnstock-ad-features\">\n", "                <li><span class=\"feature-highlight\">⚡ Đi<PERSON>u k<PERSON>ển AI viết code Python:</span> <PERSON><PERSON><PERSON>ng cần thuộc lòng cú pháp, h<PERSON><PERSON> cách dùng AI agent</li>\n", "                <li><span class=\"feature-highlight\">🎯 Từ ý tưởng đến sản phẩm:</span> 15+ bot & chương trình thực tế hoàn chỉnh trong 10 buổi học</li>\n", "                <li><span class=\"feature-highlight\">🚀 Giáo trình mới tập trung vào AI:</span> <PERSON><PERSON> dụng cách tiếp cận AI thực chiến từ 15/6/2025. <PERSON><PERSON><PERSON> cập trước nội dung khóa 10 ngay khi đăng ký.</li>\n", "                <li><span class=\"feature-highlight\">💡 Cộng đồng chất lượng cao:</span> 100+ nhà đầu tư, chuyên gia & lập trình viên cùng học hỏi</li>\n", "            </ul>\n", "            <div class=\"button-container\">\n", "                <a href=\"https://vnstocks.com/lp-khoa-hoc-python-chung-khoan\" class=\"vnstock-ad-button\">\n", "                    <PERSON><PERSON>ng ký ngay ›\n", "                </a>\n", "            </div>\n", "        </div>\n", "        <a href=\"https://vnstocks.com/lp-khoa-hoc-python-chung-khoan\" class=\"vnstock-ad-image\">\n", "            <img src=\"https://vnstocks.com/img/cta-python-chung-khoan-k11-start-now.jpg\" alt=\"Khóa học Python Coding với AI\" style=\"width: 100%; height: 100%; max-height: 250px; object-fit: cover; display: block;\">\n", "        </a>\n", "    </div>\n", "</body>\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from vnstock import Listing, Quote, Company, Finance, Trading, Screener "]}, {"cell_type": "code", "execution_count": 5, "id": "c8f0f9e6", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-02</td>\n", "      <td>16.81</td>\n", "      <td>17.37</td>\n", "      <td>16.81</td>\n", "      <td>17.16</td>\n", "      <td>13896933</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-01-03</td>\n", "      <td>17.19</td>\n", "      <td>17.55</td>\n", "      <td>17.02</td>\n", "      <td>17.55</td>\n", "      <td>9817807</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-04</td>\n", "      <td>17.69</td>\n", "      <td>18.00</td>\n", "      <td>17.62</td>\n", "      <td>17.76</td>\n", "      <td>23605373</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-05</td>\n", "      <td>17.76</td>\n", "      <td>17.86</td>\n", "      <td>17.58</td>\n", "      <td>17.86</td>\n", "      <td>9282598</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-08</td>\n", "      <td>18.04</td>\n", "      <td>18.07</td>\n", "      <td>17.69</td>\n", "      <td>17.79</td>\n", "      <td>12398885</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>295</th>\n", "      <td>2025-03-13</td>\n", "      <td>22.02</td>\n", "      <td>22.10</td>\n", "      <td>21.81</td>\n", "      <td>21.81</td>\n", "      <td>8893622</td>\n", "    </tr>\n", "    <tr>\n", "      <th>296</th>\n", "      <td>2025-03-14</td>\n", "      <td>21.89</td>\n", "      <td>21.89</td>\n", "      <td>21.64</td>\n", "      <td>21.72</td>\n", "      <td>11556328</td>\n", "    </tr>\n", "    <tr>\n", "      <th>297</th>\n", "      <td>2025-03-17</td>\n", "      <td>21.81</td>\n", "      <td>21.93</td>\n", "      <td>21.72</td>\n", "      <td>21.89</td>\n", "      <td>5668733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>298</th>\n", "      <td>2025-03-18</td>\n", "      <td>21.97</td>\n", "      <td>22.18</td>\n", "      <td>21.93</td>\n", "      <td>21.97</td>\n", "      <td>6013116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>299</th>\n", "      <td>2025-03-19</td>\n", "      <td>21.97</td>\n", "      <td>21.97</td>\n", "      <td>21.76</td>\n", "      <td>21.89</td>\n", "      <td>7918115</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>300 rows × 6 columns</p>\n", "</div>"], "text/plain": ["          time   open   high    low  close    volume\n", "0   2024-01-02  16.81  17.37  16.81  17.16  13896933\n", "1   2024-01-03  17.19  17.55  17.02  17.55   9817807\n", "2   2024-01-04  17.69  18.00  17.62  17.76  23605373\n", "3   2024-01-05  17.76  17.86  17.58  17.86   9282598\n", "4   2024-01-08  18.04  18.07  17.69  17.79  12398885\n", "..         ...    ...    ...    ...    ...       ...\n", "295 2025-03-13  22.02  22.10  21.81  21.81   8893622\n", "296 2025-03-14  21.89  21.89  21.64  21.72  11556328\n", "297 2025-03-17  21.81  21.93  21.72  21.89   5668733\n", "298 2025-03-18  21.97  22.18  21.93  21.97   6013116\n", "299 2025-03-19  21.97  21.97  21.76  21.89   7918115\n", "\n", "[300 rows x 6 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["stock = Vnstock().stock(symbol='ACB', source='VCI')\n", "stock.quote.history(start='2024-01-01', end='2025-03-19', interval='1D')"]}, {"cell_type": "code", "execution_count": 6, "id": "e865201c", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"10\" halign=\"left\">listing</th>\n", "      <th>...</th>\n", "      <th colspan=\"10\" halign=\"left\">bid_ask</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>ceiling</th>\n", "      <th>floor</th>\n", "      <th>ref_price</th>\n", "      <th>stock_type</th>\n", "      <th>exchange</th>\n", "      <th>trading_status</th>\n", "      <th>security_status</th>\n", "      <th>last_trading_date</th>\n", "      <th>listed_share</th>\n", "      <th>...</th>\n", "      <th>bid_2_price</th>\n", "      <th>bid_2_volume</th>\n", "      <th>bid_3_price</th>\n", "      <th>bid_3_volume</th>\n", "      <th>ask_1_price</th>\n", "      <th>ask_1_volume</th>\n", "      <th>ask_2_price</th>\n", "      <th>ask_2_volume</th>\n", "      <th>ask_3_price</th>\n", "      <th>ask_3_volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>VCB</td>\n", "      <td>60500</td>\n", "      <td>52700</td>\n", "      <td>56600</td>\n", "      <td>STOCK</td>\n", "      <td>HSX</td>\n", "      <td>TRADING_ACTIVATED</td>\n", "      <td>N</td>\n", "      <td>None</td>\n", "      <td>8355675094</td>\n", "      <td>...</td>\n", "      <td>56900</td>\n", "      <td>500</td>\n", "      <td>56800</td>\n", "      <td>600</td>\n", "      <td>57100</td>\n", "      <td>373700</td>\n", "      <td>57200</td>\n", "      <td>688500</td>\n", "      <td>57300</td>\n", "      <td>739000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ACB</td>\n", "      <td>22750</td>\n", "      <td>19850</td>\n", "      <td>21300</td>\n", "      <td>STOCK</td>\n", "      <td>HSX</td>\n", "      <td>TRADING_ACTIVATED</td>\n", "      <td>N</td>\n", "      <td>None</td>\n", "      <td>4466657912</td>\n", "      <td>...</td>\n", "      <td>21400</td>\n", "      <td>423000</td>\n", "      <td>21350</td>\n", "      <td>164900</td>\n", "      <td>21500</td>\n", "      <td>171100</td>\n", "      <td>21550</td>\n", "      <td>158600</td>\n", "      <td>21600</td>\n", "      <td>554900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TCB</td>\n", "      <td>36350</td>\n", "      <td>31650</td>\n", "      <td>34000</td>\n", "      <td>STOCK</td>\n", "      <td>HSX</td>\n", "      <td>TRADING_ACTIVATED</td>\n", "      <td>N</td>\n", "      <td>None</td>\n", "      <td>7064851739</td>\n", "      <td>...</td>\n", "      <td>33950</td>\n", "      <td>211100</td>\n", "      <td>33900</td>\n", "      <td>247900</td>\n", "      <td>34050</td>\n", "      <td>220200</td>\n", "      <td>34100</td>\n", "      <td>300700</td>\n", "      <td>34150</td>\n", "      <td>111200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>BID</td>\n", "      <td>38300</td>\n", "      <td>33300</td>\n", "      <td>35800</td>\n", "      <td>STOCK</td>\n", "      <td>HSX</td>\n", "      <td>TRADING_ACTIVATED</td>\n", "      <td>N</td>\n", "      <td>None</td>\n", "      <td>7021361917</td>\n", "      <td>...</td>\n", "      <td>35900</td>\n", "      <td>36500</td>\n", "      <td>35850</td>\n", "      <td>58400</td>\n", "      <td>36000</td>\n", "      <td>238400</td>\n", "      <td>36050</td>\n", "      <td>16200</td>\n", "      <td>36100</td>\n", "      <td>119500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4 rows × 61 columns</p>\n", "</div>"], "text/plain": ["  listing                                                                  \\\n", "   symbol ceiling  floor ref_price stock_type exchange     trading_status   \n", "0     VCB   60500  52700     56600      STOCK      HSX  TRADING_ACTIVATED   \n", "1     ACB   22750  19850     21300      STOCK      HSX  TRADING_ACTIVATED   \n", "2     TCB   36350  31650     34000      STOCK      HSX  TRADING_ACTIVATED   \n", "3     BID   38300  33300     35800      STOCK      HSX  TRADING_ACTIVATED   \n", "\n", "                                                  ...     bid_ask  \\\n", "  security_status last_trading_date listed_share  ... bid_2_price   \n", "0               N              None   8355675094  ...       56900   \n", "1               N              None   4466657912  ...       21400   \n", "2               N              None   7064851739  ...       33950   \n", "3               N              None   7021361917  ...       35900   \n", "\n", "                                                                              \\\n", "  bid_2_volume bid_3_price bid_3_volume ask_1_price ask_1_volume ask_2_price   \n", "0          500       56800          600       57100       373700       57200   \n", "1       423000       21350       164900       21500       171100       21550   \n", "2       211100       33900       247900       34050       220200       34100   \n", "3        36500       35850        58400       36000       238400       36050   \n", "\n", "                                         \n", "  ask_2_volume ask_3_price ask_3_volume  \n", "0       688500       57300       739000  \n", "1       158600       21600       554900  \n", "2       300700       34150       111200  \n", "3        16200       36100       119500  \n", "\n", "[4 rows x 61 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Trading(source='VCI').price_board(['VCB','ACB','TCB','BID'])"]}, {"cell_type": "code", "execution_count": 7, "id": "5c23174e", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>id</th>\n", "      <th>issue_share</th>\n", "      <th>history</th>\n", "      <th>company_profile</th>\n", "      <th>icb_name3</th>\n", "      <th>icb_name2</th>\n", "      <th>icb_name4</th>\n", "      <th>financial_ratio_issue_share</th>\n", "      <th>charter_capital</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ACB</td>\n", "      <td>67505</td>\n", "      <td>4466657912</td>\n", "      <td>- Ngày 24/04/1993: <PERSON><PERSON> hàng <PERSON> mại <PERSON> ph...</td>\n", "      <td><PERSON><PERSON> hàng <PERSON>ng mại <PERSON> phần <PERSON> (ACB) được...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>5136656599</td>\n", "      <td>44666579120000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol     id  issue_share  \\\n", "0    ACB  67505   4466657912   \n", "\n", "                                             history  \\\n", "0   - Ngày 24/04/1993: <PERSON><PERSON> hàng <PERSON> mại <PERSON> ph...   \n", "\n", "                                     company_profile  icb_name3  icb_name2  \\\n", "0  <PERSON><PERSON> hàng Thương mại Cổ phần <PERSON> (ACB) được...  Ngân hàng  Ngân hàng   \n", "\n", "   icb_name4  financial_ratio_issue_share  charter_capital  \n", "0  Ngân hàng                   5136656599   44666579120000  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["company = Vnstock().stock(symbol='ACB', source='VCI').company\n", "company.overview()"]}, {"cell_type": "code", "execution_count": 8, "id": "d587a3c3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"3\" halign=\"left\">Meta</th>\n", "      <th colspan=\"4\" halign=\"left\">Chỉ tiêu cơ cấu nguồn vốn</th>\n", "      <th colspan=\"3\" halign=\"left\">Chỉ tiêu hiệu quả hoạt động</th>\n", "      <th>...</th>\n", "      <th>Chỉ tiêu thanh k<PERSON>n</th>\n", "      <th colspan=\"9\" halign=\"left\">Chỉ tiêu định giá</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>CP</th>\n", "      <th>Năm</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>(Vay NH+DH)/VCSH</th>\n", "      <th>Nợ/VCSH</th>\n", "      <th>TSCĐ / Vốn CSH</th>\n", "      <th>Vốn <PERSON>H/Vốn điều lệ</th>\n", "      <th>Vòng quay tài sản</th>\n", "      <th>Vòng quay TSCĐ</th>\n", "      <th><PERSON><PERSON> ngày thu tiền bình quân</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON><PERSON> b<PERSON>y tài ch<PERSON></th>\n", "      <th>P/B</th>\n", "      <th><PERSON><PERSON><PERSON> (Tỷ đồng)</th>\n", "      <th>Số CP <PERSON> (Triệu CP)</th>\n", "      <th>P/E</th>\n", "      <th>P/S</th>\n", "      <th>P/Cash Flow</th>\n", "      <th>EPS (VND)</th>\n", "      <th>BVPS (VND)</th>\n", "      <th>EV/EBITDA</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>VCI</td>\n", "      <td>2024</td>\n", "      <td>5</td>\n", "      <td>0.971382</td>\n", "      <td>1.054349</td>\n", "      <td>0.002879</td>\n", "      <td>1.802578</td>\n", "      <td>0.168564</td>\n", "      <td>121.775022</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>2.054349</td>\n", "      <td>1.974949</td>\n", "      <td>25564341488000</td>\n", "      <td>718099480</td>\n", "      <td>28.071333</td>\n", "      <td>6.917647</td>\n", "      <td>-7.554365</td>\n", "      <td>1268.197706</td>\n", "      <td>18025.783068</td>\n", "      <td>18.108073</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>VCI</td>\n", "      <td>2023</td>\n", "      <td>5</td>\n", "      <td>1.218152</td>\n", "      <td>1.340876</td>\n", "      <td>0.003178</td>\n", "      <td>1.026491</td>\n", "      <td>0.156992</td>\n", "      <td>105.385605</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>2.340876</td>\n", "      <td>1.970499</td>\n", "      <td>14525000000000</td>\n", "      <td>437500000</td>\n", "      <td>29.528080</td>\n", "      <td>5.874715</td>\n", "      <td>-3.334822</td>\n", "      <td>1124.353507</td>\n", "      <td>16848.524590</td>\n", "      <td>17.525975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>VCI</td>\n", "      <td>2022</td>\n", "      <td>5</td>\n", "      <td>0.973942</td>\n", "      <td>1.192721</td>\n", "      <td>0.003617</td>\n", "      <td>0.904536</td>\n", "      <td>0.204426</td>\n", "      <td>172.225781</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>2.192721</td>\n", "      <td>2.866248</td>\n", "      <td>18617620767750</td>\n", "      <td>435499901</td>\n", "      <td>21.424727</td>\n", "      <td>5.898666</td>\n", "      <td>4.975461</td>\n", "      <td>1995.357941</td>\n", "      <td>14914.970490</td>\n", "      <td>12.729467</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>VCI</td>\n", "      <td>2021</td>\n", "      <td>5</td>\n", "      <td>0.972610</td>\n", "      <td>1.543047</td>\n", "      <td>0.002011</td>\n", "      <td>0.910997</td>\n", "      <td>0.296344</td>\n", "      <td>269.213152</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>2.543047</td>\n", "      <td>1.183493</td>\n", "      <td>7742250000000</td>\n", "      <td>333000000</td>\n", "      <td>5.165914</td>\n", "      <td>2.088510</td>\n", "      <td>-2.064593</td>\n", "      <td>4500.655782</td>\n", "      <td>19645.234971</td>\n", "      <td>5.895364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>VCI</td>\n", "      <td>2020</td>\n", "      <td>5</td>\n", "      <td>0.596293</td>\n", "      <td>0.853905</td>\n", "      <td>0.003181</td>\n", "      <td>0.629646</td>\n", "      <td>0.221383</td>\n", "      <td>142.402909</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>1.853905</td>\n", "      <td>2.666309</td>\n", "      <td>12055680000000</td>\n", "      <td>165600000</td>\n", "      <td>15.678875</td>\n", "      <td>6.970247</td>\n", "      <td>-21.093469</td>\n", "      <td>4643.190154</td>\n", "      <td>27303.662559</td>\n", "      <td>12.313747</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>VCI</td>\n", "      <td>2019</td>\n", "      <td>5</td>\n", "      <td>0.358125</td>\n", "      <td>0.787676</td>\n", "      <td>0.002445</td>\n", "      <td>0.564212</td>\n", "      <td>0.224089</td>\n", "      <td>109.544483</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>1.787676</td>\n", "      <td>2.418360</td>\n", "      <td>9798240000000</td>\n", "      <td>164400000</td>\n", "      <td>14.135191</td>\n", "      <td>6.358594</td>\n", "      <td>-28.722550</td>\n", "      <td>4216.426869</td>\n", "      <td>24644.804444</td>\n", "      <td>10.220559</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>VCI</td>\n", "      <td>2018</td>\n", "      <td>5</td>\n", "      <td>0.270345</td>\n", "      <td>0.786999</td>\n", "      <td>0.005003</td>\n", "      <td>0.507308</td>\n", "      <td>0.282137</td>\n", "      <td>83.645260</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>1.786999</td>\n", "      <td>2.666718</td>\n", "      <td>9714793444000</td>\n", "      <td>162999890</td>\n", "      <td>11.639758</td>\n", "      <td>5.333463</td>\n", "      <td>12.398987</td>\n", "      <td>5120.381537</td>\n", "      <td>22349.571713</td>\n", "      <td>7.529021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>VCI</td>\n", "      <td>2017</td>\n", "      <td>5</td>\n", "      <td>0.475082</td>\n", "      <td>1.120296</td>\n", "      <td>0.008388</td>\n", "      <td>0.420470</td>\n", "      <td>0.323097</td>\n", "      <td>88.278011</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>2.120296</td>\n", "      <td>2.368686</td>\n", "      <td>7152000000000</td>\n", "      <td>120000000</td>\n", "      <td>10.917481</td>\n", "      <td>4.654003</td>\n", "      <td>-3.614195</td>\n", "      <td>5459.134708</td>\n", "      <td>25161.626644</td>\n", "      <td>8.311031</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>VCI</td>\n", "      <td>2016</td>\n", "      <td>5</td>\n", "      <td>0.491563</td>\n", "      <td>1.436668</td>\n", "      <td>0.007434</td>\n", "      <td>0.177769</td>\n", "      <td>0.320657</td>\n", "      <td>73.282490</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>2.436668</td>\n", "      <td>5.128414</td>\n", "      <td>6150720000000</td>\n", "      <td>103200000</td>\n", "      <td>18.357534</td>\n", "      <td>6.882671</td>\n", "      <td>-46.597333</td>\n", "      <td>3246.623509</td>\n", "      <td>11621.527734</td>\n", "      <td>11.306346</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>VCI</td>\n", "      <td>2015</td>\n", "      <td>5</td>\n", "      <td>0.550475</td>\n", "      <td>1.532230</td>\n", "      <td>0.015316</td>\n", "      <td>0.135468</td>\n", "      <td>0.267767</td>\n", "      <td>41.241080</td>\n", "      <td>21.294522</td>\n", "      <td>...</td>\n", "      <td>2.532230</td>\n", "      <td>3.318337</td>\n", "      <td>2980000000000</td>\n", "      <td>50000000</td>\n", "      <td>12.563335</td>\n", "      <td>4.252882</td>\n", "      <td>-7.172009</td>\n", "      <td>4743.963391</td>\n", "      <td>17960.802319</td>\n", "      <td>8.174017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>VCI</td>\n", "      <td>2014</td>\n", "      <td>5</td>\n", "      <td>0.364664</td>\n", "      <td>2.686542</td>\n", "      <td>0.022791</td>\n", "      <td>0.112364</td>\n", "      <td>0.251576</td>\n", "      <td>40.964295</td>\n", "      <td>31.374424</td>\n", "      <td>...</td>\n", "      <td>3.433360</td>\n", "      <td>4.077489</td>\n", "      <td>2980000000000</td>\n", "      <td>50000000</td>\n", "      <td>20.513135</td>\n", "      <td>4.832263</td>\n", "      <td>4.521565</td>\n", "      <td>2905.455386</td>\n", "      <td>14616.838928</td>\n", "      <td>14.166357</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>VCI</td>\n", "      <td>2013</td>\n", "      <td>5</td>\n", "      <td>1.191585</td>\n", "      <td>2.918571</td>\n", "      <td>0.020265</td>\n", "      <td>0.075776</td>\n", "      <td>0.180038</td>\n", "      <td>30.100257</td>\n", "      <td>149.353356</td>\n", "      <td>...</td>\n", "      <td>3.918571</td>\n", "      <td>4.363627</td>\n", "      <td>2374464000000</td>\n", "      <td>39840000</td>\n", "      <td>32.901021</td>\n", "      <td>5.944203</td>\n", "      <td>28.472663</td>\n", "      <td>1811.493953</td>\n", "      <td>13658.362879</td>\n", "      <td>28.466190</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>VCI</td>\n", "      <td>2011</td>\n", "      <td>5</td>\n", "      <td>3.426721</td>\n", "      <td>4.235392</td>\n", "      <td>0.016485</td>\n", "      <td>0.064307</td>\n", "      <td>0.221780</td>\n", "      <td>45.438230</td>\n", "      <td>328.174272</td>\n", "      <td>...</td>\n", "      <td>5.235392</td>\n", "      <td>4.878577</td>\n", "      <td>2252880000000</td>\n", "      <td>37800000</td>\n", "      <td>90.235922</td>\n", "      <td>4.857922</td>\n", "      <td>-22.732493</td>\n", "      <td>660.490840</td>\n", "      <td>12216.677603</td>\n", "      <td>79.644405</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>13 rows × 37 columns</p>\n", "</div>"], "text/plain": ["   Meta          Chỉ tiêu cơ cấu nguồn vốn                           \\\n", "     CP   N<PERSON><PERSON>          (Vay NH+DH)/VCSH   Nợ/VCSH TSCĐ / Vốn CSH   \n", "0   VCI  2024  5                  0.971382  1.054349       0.002879   \n", "1   VCI  2023  5                  1.218152  1.340876       0.003178   \n", "2   VCI  2022  5                  0.973942  1.192721       0.003617   \n", "3   VCI  2021  5                  0.972610  1.543047       0.002011   \n", "4   VCI  2020  5                  0.596293  0.853905       0.003181   \n", "5   VCI  2019  5                  0.358125  0.787676       0.002445   \n", "6   VCI  2018  5                  0.270345  0.786999       0.005003   \n", "7   VCI  2017  5                  0.475082  1.120296       0.008388   \n", "8   VCI  2016  5                  0.491563  1.436668       0.007434   \n", "9   VCI  2015  5                  0.550475  1.532230       0.015316   \n", "10  VCI  2014  5                  0.364664  2.686542       0.022791   \n", "11  VCI  2013  5                  1.191585  2.918571       0.020265   \n", "12  VCI  2011  5                  3.426721  4.235392       0.016485   \n", "\n", "                       Chỉ tiêu hiệu quả hoạt động                 \\\n", "   Vốn CSH/Vốn điều lệ           Vòng quay tài sản Vòng quay TSCĐ   \n", "0             1.802578                    0.168564     121.775022   \n", "1             1.026491                    0.156992     105.385605   \n", "2             0.904536                    0.204426     172.225781   \n", "3             0.910997                    0.296344     269.213152   \n", "4             0.629646                    0.221383     142.402909   \n", "5             0.564212                    0.224089     109.544483   \n", "6             0.507308                    0.282137      83.645260   \n", "7             0.420470                    0.323097      88.278011   \n", "8             0.177769                    0.320657      73.282490   \n", "9             0.135468                    0.267767      41.241080   \n", "10            0.112364                    0.251576      40.964295   \n", "11            0.075776                    0.180038      30.100257   \n", "12            0.064307                    0.221780      45.438230   \n", "\n", "                               ... Chỉ tiêu thanh khoản Chỉ tiêu định giá  \\\n", "   <PERSON>ố ngày thu tiền bình quân  ...    <PERSON><PERSON>n bẩy tài ch<PERSON>h               P/B   \n", "0                    0.000000  ...             2.054349          1.974949   \n", "1                    0.000000  ...             2.340876          1.970499   \n", "2                    0.000000  ...             2.192721          2.866248   \n", "3                    0.000000  ...             2.543047          1.183493   \n", "4                    0.000000  ...             1.853905          2.666309   \n", "5                    0.000000  ...             1.787676          2.418360   \n", "6                    0.000000  ...             1.786999          2.666718   \n", "7                    0.000000  ...             2.120296          2.368686   \n", "8                    0.000000  ...             2.436668          5.128414   \n", "9                   21.294522  ...             2.532230          3.318337   \n", "10                  31.374424  ...             3.433360          4.077489   \n", "11                 149.353356  ...             3.918571          4.363627   \n", "12                 328.174272  ...             5.235392          4.878577   \n", "\n", "                                                                     \\\n", "   <PERSON><PERSON><PERSON> (Tỷ đồng) Số CP <PERSON> (Triệu CP)        P/E       P/S   \n", "0     25564341488000                 718099480  28.071333  6.917647   \n", "1     14525000000000                 437500000  29.528080  5.874715   \n", "2     18617620767750                 435499901  21.424727  5.898666   \n", "3      7742250000000                 333000000   5.165914  2.088510   \n", "4     12055680000000                 165600000  15.678875  6.970247   \n", "5      9798240000000                 164400000  14.135191  6.358594   \n", "6      9714793444000                 162999890  11.639758  5.333463   \n", "7      7152000000000                 120000000  10.917481  4.654003   \n", "8      6150720000000                 103200000  18.357534  6.882671   \n", "9      2980000000000                  50000000  12.563335  4.252882   \n", "10     2980000000000                  50000000  20.513135  4.832263   \n", "11     2374464000000                  39840000  32.901021  5.944203   \n", "12     2252880000000                  37800000  90.235922  4.857922   \n", "\n", "                                                      \n", "   P/Cash Flow    EPS (VND)    BVPS (VND)  EV/EBITDA  \n", "0    -7.554365  1268.197706  18025.783068  18.108073  \n", "1    -3.334822  1124.353507  16848.524590  17.525975  \n", "2     4.975461  1995.357941  14914.970490  12.729467  \n", "3    -2.064593  4500.655782  19645.234971   5.895364  \n", "4   -21.093469  4643.190154  27303.662559  12.313747  \n", "5   -28.722550  4216.426869  24644.804444  10.220559  \n", "6    12.398987  5120.381537  22349.571713   7.529021  \n", "7    -3.614195  5459.134708  25161.626644   8.311031  \n", "8   -46.597333  3246.623509  11621.527734  11.306346  \n", "9    -7.172009  4743.963391  17960.802319   8.174017  \n", "10    4.521565  2905.455386  14616.838928  14.166357  \n", "11   28.472663  1811.493953  13658.362879  28.466190  \n", "12  -22.732493   660.490840  12216.677603  79.644405  \n", "\n", "[13 rows x 37 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["stock = Vnstock().stock(symbol='VCI', source='VCI')\n", "# <PERSON><PERSON><PERSON> cân đối kế toán - năm\n", "stock.finance.balance_sheet(period='year', lang='vi', dropna=True)\n", "# <PERSON><PERSON><PERSON> cân đối kế toán - quý\n", "stock.finance.balance_sheet(period='quarter', lang='en', dropna=True)\n", "# <PERSON><PERSON><PERSON> quả hoạt động kinh doanh\n", "stock.finance.income_statement(period='year', lang='vi', dropna=True)\n", "# <PERSON><PERSON><PERSON> chuyển tiền tệ\n", "stock.finance.cash_flow(period='year', dropna=True)\n", "# Chỉ số tài ch<PERSON>h\n", "stock.finance.ratio(period='year', lang='vi', dropna=True)"]}, {"cell_type": "code", "execution_count": 9, "id": "063e66be", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ticker</th>\n", "      <th>exchange</th>\n", "      <th>industry</th>\n", "      <th>market_cap</th>\n", "      <th>roe</th>\n", "      <th>stock_rating</th>\n", "      <th>business_operation</th>\n", "      <th>business_model</th>\n", "      <th>financial_health</th>\n", "      <th>alpha</th>\n", "      <th>...</th>\n", "      <th>nim</th>\n", "      <th>price_vs_sma200</th>\n", "      <th>eps_ttm_growth1_year</th>\n", "      <th>eps_ttm_growth5_year</th>\n", "      <th>equity_mi</th>\n", "      <th>eps_recently</th>\n", "      <th>percent_price_vs_ma200</th>\n", "      <th>percent_price_vs_ma20</th>\n", "      <th>percent_price_vs_ma50</th>\n", "      <th>percent_price_vs_ma100</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A32</td>\n", "      <td>UPCOM</td>\n", "      <td><PERSON><PERSON><PERSON> cá nhân &amp; <PERSON><PERSON></td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.7</td>\n", "      <td>3.5</td>\n", "      <td>2.6</td>\n", "      <td>0.1</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AAA</td>\n", "      <td>HSX</td>\n", "      <td><PERSON><PERSON><PERSON> chấ<PERSON></td>\n", "      <td>2737.0</td>\n", "      <td>5.1</td>\n", "      <td>2.4</td>\n", "      <td>1.5</td>\n", "      <td>3.0</td>\n", "      <td>4.0</td>\n", "      <td>-0.2</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>-24.52</td>\n", "      <td>-10.77</td>\n", "      <td>5388.0</td>\n", "      <td>730.0</td>\n", "      <td>-11.9</td>\n", "      <td>-1.0</td>\n", "      <td>3.0</td>\n", "      <td>-5.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AAH</td>\n", "      <td>UPCOM</td>\n", "      <td><PERSON><PERSON><PERSON> bản</td>\n", "      <td>467.0</td>\n", "      <td>2.5</td>\n", "      <td>2.5</td>\n", "      <td>1.2</td>\n", "      <td>NaN</td>\n", "      <td>4.6</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>{'vi': '<PERSON><PERSON><PERSON> cắt lên <PERSON>(200)', 'en': '<PERSON> cr...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1208.0</td>\n", "      <td>257.0</td>\n", "      <td>0.5</td>\n", "      <td>-1.1</td>\n", "      <td>-1.1</td>\n", "      <td>-6.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AAM</td>\n", "      <td>HSX</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> phẩm và đồ uống</td>\n", "      <td>73.0</td>\n", "      <td>-1.5</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>4.2</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>198.0</td>\n", "      <td>-289.0</td>\n", "      <td>-1.1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AAS</td>\n", "      <td>UPCOM</td>\n", "      <td><PERSON><PERSON><PERSON> v<PERSON> tài ch<PERSON></td>\n", "      <td>2030.0</td>\n", "      <td>2.6</td>\n", "      <td>2.4</td>\n", "      <td>3.8</td>\n", "      <td>2.8</td>\n", "      <td>2.6</td>\n", "      <td>0.1</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>-37.27</td>\n", "      <td>NaN</td>\n", "      <td>2522.0</td>\n", "      <td>286.0</td>\n", "      <td>12.8</td>\n", "      <td>-1.3</td>\n", "      <td>6.7</td>\n", "      <td>4.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1630</th>\n", "      <td>XPH</td>\n", "      <td>UPCOM</td>\n", "      <td><PERSON><PERSON><PERSON> cá nhân &amp; <PERSON><PERSON></td>\n", "      <td>155.0</td>\n", "      <td>-5.3</td>\n", "      <td>NaN</td>\n", "      <td>1.2</td>\n", "      <td>NaN</td>\n", "      <td>4.2</td>\n", "      <td>0.6</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>142.0</td>\n", "      <td>-595.0</td>\n", "      <td>74.3</td>\n", "      <td>11.3</td>\n", "      <td>32.4</td>\n", "      <td>47.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1631</th>\n", "      <td>YBC</td>\n", "      <td>UPCOM</td>\n", "      <td><PERSON><PERSON><PERSON> d<PERSON>ng và V<PERSON>t li<PERSON></td>\n", "      <td>138.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.5</td>\n", "      <td>3.5</td>\n", "      <td>1.6</td>\n", "      <td>0.4</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-1674.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1632</th>\n", "      <td>YBM</td>\n", "      <td>HSX</td>\n", "      <td><PERSON><PERSON><PERSON> bản</td>\n", "      <td>206.0</td>\n", "      <td>6.3</td>\n", "      <td>NaN</td>\n", "      <td>2.1</td>\n", "      <td>3.7</td>\n", "      <td>2.2</td>\n", "      <td>0.2</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>-12.68</td>\n", "      <td>16.10</td>\n", "      <td>189.0</td>\n", "      <td>823.0</td>\n", "      <td>12.9</td>\n", "      <td>-2.5</td>\n", "      <td>-4.0</td>\n", "      <td>-1.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1633</th>\n", "      <td>YEG</td>\n", "      <td>HSX</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON> thông</td>\n", "      <td>2388.0</td>\n", "      <td>7.9</td>\n", "      <td>2.4</td>\n", "      <td>1.6</td>\n", "      <td>3.0</td>\n", "      <td>4.8</td>\n", "      <td>0.3</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>207.18</td>\n", "      <td>NaN</td>\n", "      <td>2017.0</td>\n", "      <td>704.0</td>\n", "      <td>2.0</td>\n", "      <td>1.7</td>\n", "      <td>5.2</td>\n", "      <td>-6.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1634</th>\n", "      <td>YTC</td>\n", "      <td>UPCOM</td>\n", "      <td>Y tế</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.4</td>\n", "      <td>3.5</td>\n", "      <td>2.4</td>\n", "      <td>0.3</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-16.2</td>\n", "      <td>2.6</td>\n", "      <td>-6.0</td>\n", "      <td>-5.6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1635 rows × 109 columns</p>\n", "</div>"], "text/plain": ["     ticker exchange                 industry  market_cap  roe  stock_rating  \\\n", "0       A32    UPCOM  Hàng cá nhân & Gia dụng         NaN  NaN           NaN   \n", "1       AAA      HSX                 Hóa chất      2737.0  5.1           2.4   \n", "2       AAH    UPCOM        Tài nguyên <PERSON> bản       467.0  2.5           2.5   \n", "3       AAM      HSX     Thự<PERSON> phẩm và đồ uống        73.0 -1.5           NaN   \n", "4       AAS    UPCOM        Dịch vụ tài chính      2030.0  2.6           2.4   \n", "...     ...      ...                      ...         ...  ...           ...   \n", "1630    XPH    UPCOM  Hàng cá nhân & Gia dụng       155.0 -5.3           NaN   \n", "1631    YBC    UPCOM     Xây dựng và Vật liệu       138.0  NaN           NaN   \n", "1632    YBM      HSX        Tài nguyên <PERSON> bản       206.0  6.3           NaN   \n", "1633    YEG      HSX             Truyền thông      2388.0  7.9           2.4   \n", "1634    YTC    UPCOM                     Y tế         NaN  NaN           NaN   \n", "\n", "      business_operation  business_model  financial_health  alpha  ...  nim  \\\n", "0                    1.7             3.5               2.6    0.1  ...  NaN   \n", "1                    1.5             3.0               4.0   -0.2  ...  NaN   \n", "2                    1.2             NaN               4.6    0.0  ...  NaN   \n", "3                    1.0             NaN               4.2    0.0  ...  NaN   \n", "4                    3.8             2.8               2.6    0.1  ...  NaN   \n", "...                  ...             ...               ...    ...  ...  ...   \n", "1630                 1.2             NaN               4.2    0.6  ...  NaN   \n", "1631                 2.5             3.5               1.6    0.4  ...  NaN   \n", "1632                 2.1             3.7               2.2    0.2  ...  NaN   \n", "1633                 1.6             3.0               4.8    0.3  ...  NaN   \n", "1634                 1.4             3.5               2.4    0.3  ...  NaN   \n", "\n", "                                        price_vs_sma200  eps_ttm_growth1_year  \\\n", "0                                                  None                   NaN   \n", "1                                                  None                -24.52   \n", "2     {'vi': '<PERSON><PERSON><PERSON> cắt lên <PERSON>(200)', 'en': '<PERSON> cr...                   NaN   \n", "3                                                  None                   NaN   \n", "4                                                  None                -37.27   \n", "...                                                 ...                   ...   \n", "1630                                               None                   NaN   \n", "1631                                               None                   NaN   \n", "1632                                               None                -12.68   \n", "1633                                               None                207.18   \n", "1634                                               None                   NaN   \n", "\n", "      eps_ttm_growth5_year  equity_mi  eps_recently  percent_price_vs_ma200  \\\n", "0                      NaN        NaN           NaN                     NaN   \n", "1                   -10.77     5388.0         730.0                   -11.9   \n", "2                      NaN     1208.0         257.0                     0.5   \n", "3                      NaN      198.0        -289.0                    -1.1   \n", "4                      NaN     2522.0         286.0                    12.8   \n", "...                    ...        ...           ...                     ...   \n", "1630                   NaN      142.0        -595.0                    74.3   \n", "1631                   NaN        NaN       -1674.0                     NaN   \n", "1632                 16.10      189.0         823.0                    12.9   \n", "1633                   NaN     2017.0         704.0                     2.0   \n", "1634                   NaN        NaN           NaN                   -16.2   \n", "\n", "      percent_price_vs_ma20  percent_price_vs_ma50  percent_price_vs_ma100  \n", "0                       NaN                    NaN                     NaN  \n", "1                      -1.0                    3.0                    -5.7  \n", "2                      -1.1                   -1.1                    -6.8  \n", "3                       0.0                    0.0                    -0.2  \n", "4                      -1.3                    6.7                     4.6  \n", "...                     ...                    ...                     ...  \n", "1630                   11.3                   32.4                    47.7  \n", "1631                    NaN                    NaN                     NaN  \n", "1632                   -2.5                   -4.0                    -1.7  \n", "1633                    1.7                    5.2                    -6.8  \n", "1634                    2.6                   -6.0                    -5.6  \n", "\n", "[1635 rows x 109 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["stock.screener.stock(params={\"exchangeName\": \"HOSE,HNX,UPCOM\"}, limit=1700)"]}, {"cell_type": "code", "execution_count": 11, "id": "1bc7a357", "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-20 20:39:55 - vnstock.explorer.fmarket.fund - INFO - Total number of funds currently listed on Fmarket: 56\n", "2025-06-20 20:39:56 - vnstock.explorer.fmarket.fund - INFO - Total number of funds currently listed on Fmarket: 56\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>short_name</th>\n", "      <th>name</th>\n", "      <th>fund_type</th>\n", "      <th>fund_owner_name</th>\n", "      <th>management_fee</th>\n", "      <th>inception_date</th>\n", "      <th>nav</th>\n", "      <th>nav_change_previous</th>\n", "      <th>nav_change_last_year</th>\n", "      <th>nav_change_inception</th>\n", "      <th>...</th>\n", "      <th>nav_change_3m</th>\n", "      <th>nav_change_6m</th>\n", "      <th>nav_change_12m</th>\n", "      <th>nav_change_24m</th>\n", "      <th>nav_change_36m</th>\n", "      <th>nav_change_36m_annualized</th>\n", "      <th>nav_update_at</th>\n", "      <th>fund_id_fmarket</th>\n", "      <th>fund_code</th>\n", "      <th>vsd_fee_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DCDS</td>\n", "      <td>QUỸ ĐẦU TƯ CHỨNG KHOÁN NĂNG ĐỘNG DC</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ DRAGON CAPITAL VIỆ...</td>\n", "      <td>1.95</td>\n", "      <td>2004-05-19</td>\n", "      <td>87007.21</td>\n", "      <td>0.44</td>\n", "      <td>6.60</td>\n", "      <td>209.12</td>\n", "      <td>...</td>\n", "      <td>8.47</td>\n", "      <td>8.37</td>\n", "      <td>5.90</td>\n", "      <td>48.42</td>\n", "      <td>46.63</td>\n", "      <td>13.61</td>\n", "      <td>2025-06-20</td>\n", "      <td>28</td>\n", "      <td>VFMVF1</td>\n", "      <td>VFMVF1N001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MBVF</td>\n", "      <td>QUỸ ĐẦU TƯ GIÁ TRỊ MB CAPITAL</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ MB</td>\n", "      <td>1.50</td>\n", "      <td>2014-04-24</td>\n", "      <td>24375.00</td>\n", "      <td>0.63</td>\n", "      <td>9.62</td>\n", "      <td>70.61</td>\n", "      <td>...</td>\n", "      <td>5.11</td>\n", "      <td>12.18</td>\n", "      <td>11.79</td>\n", "      <td>36.36</td>\n", "      <td>46.20</td>\n", "      <td>13.50</td>\n", "      <td>2025-06-20</td>\n", "      <td>47</td>\n", "      <td>MBVF</td>\n", "      <td>MBVFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SSISCA</td>\n", "      <td>QUỸ ĐẦU TƯ LỢI THẾ CẠNH TRANH BỀN VỮNG SSI</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ SSI</td>\n", "      <td>1.75</td>\n", "      <td>2014-09-25</td>\n", "      <td>40380.65</td>\n", "      <td>0.32</td>\n", "      <td>-0.99</td>\n", "      <td>180.18</td>\n", "      <td>...</td>\n", "      <td>-1.44</td>\n", "      <td>0.83</td>\n", "      <td>1.58</td>\n", "      <td>47.33</td>\n", "      <td>42.64</td>\n", "      <td>12.57</td>\n", "      <td>2025-06-20</td>\n", "      <td>11</td>\n", "      <td>SSISCA</td>\n", "      <td>SSISCAN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>VCBF-MGF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU TĂNG TRƯỞNG VCBF</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN VI...</td>\n", "      <td>1.90</td>\n", "      <td>2021-10-28</td>\n", "      <td>13372.16</td>\n", "      <td>0.13</td>\n", "      <td>-0.46</td>\n", "      <td>33.72</td>\n", "      <td>...</td>\n", "      <td>-0.44</td>\n", "      <td>0.86</td>\n", "      <td>6.00</td>\n", "      <td>40.76</td>\n", "      <td>40.59</td>\n", "      <td>12.03</td>\n", "      <td>2025-06-20</td>\n", "      <td>46</td>\n", "      <td>VCBFMGF</td>\n", "      <td>VCBFMGFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>BVPF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU TRIỂN VỌNG BẢO VIỆT</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ BẢO VIỆT</td>\n", "      <td>1.50</td>\n", "      <td>2016-12-28</td>\n", "      <td>20832.00</td>\n", "      <td>0.30</td>\n", "      <td>-2.38</td>\n", "      <td>108.28</td>\n", "      <td>...</td>\n", "      <td>-1.62</td>\n", "      <td>-0.97</td>\n", "      <td>-0.13</td>\n", "      <td>33.49</td>\n", "      <td>33.06</td>\n", "      <td>9.99</td>\n", "      <td>2025-06-20</td>\n", "      <td>14</td>\n", "      <td>BVPF</td>\n", "      <td>BVPFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>VCBF-BCF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU HÀNG ĐẦU VCBF</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN VI...</td>\n", "      <td>1.90</td>\n", "      <td>2014-08-21</td>\n", "      <td>36573.99</td>\n", "      <td>0.19</td>\n", "      <td>2.05</td>\n", "      <td>87.12</td>\n", "      <td>...</td>\n", "      <td>2.83</td>\n", "      <td>3.79</td>\n", "      <td>7.57</td>\n", "      <td>38.38</td>\n", "      <td>32.33</td>\n", "      <td>9.79</td>\n", "      <td>2025-06-20</td>\n", "      <td>32</td>\n", "      <td>VCBFBCF</td>\n", "      <td>VCBFBCFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>DCDE</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU TẬP TRUNG CỔ TỨC DC</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ DRAGON CAPITAL VIỆ...</td>\n", "      <td>1.93</td>\n", "      <td>2008-02-27</td>\n", "      <td>27778.39</td>\n", "      <td>0.31</td>\n", "      <td>2.32</td>\n", "      <td>143.35</td>\n", "      <td>...</td>\n", "      <td>4.18</td>\n", "      <td>4.27</td>\n", "      <td>2.22</td>\n", "      <td>34.64</td>\n", "      <td>31.19</td>\n", "      <td>9.47</td>\n", "      <td>2025-06-20</td>\n", "      <td>25</td>\n", "      <td>VFMVF4</td>\n", "      <td>VFMVF4N001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>VCBF-TBF</td>\n", "      <td>QUỸ ĐẦU TƯ CÂN BẰNG CHIẾN LƯỢC VCBF</td>\n", "      <td><PERSON><PERSON><PERSON> cân bằng</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN VI...</td>\n", "      <td>1.50</td>\n", "      <td>2013-12-23</td>\n", "      <td>33858.91</td>\n", "      <td>0.20</td>\n", "      <td>1.85</td>\n", "      <td>69.75</td>\n", "      <td>...</td>\n", "      <td>2.05</td>\n", "      <td>2.96</td>\n", "      <td>6.46</td>\n", "      <td>31.48</td>\n", "      <td>31.07</td>\n", "      <td>9.44</td>\n", "      <td>2025-06-20</td>\n", "      <td>31</td>\n", "      <td>VCBFTBF</td>\n", "      <td>VCBFTBFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>VCAMBF</td>\n", "      <td>QUỸ ĐẦU TƯ CÂN BẰNG BẢN VIỆT</td>\n", "      <td><PERSON><PERSON><PERSON> cân bằng</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN...</td>\n", "      <td>1.20</td>\n", "      <td>2014-04-10</td>\n", "      <td>19309.99</td>\n", "      <td>-0.12</td>\n", "      <td>2.43</td>\n", "      <td>62.02</td>\n", "      <td>...</td>\n", "      <td>2.84</td>\n", "      <td>4.08</td>\n", "      <td>2.30</td>\n", "      <td>29.80</td>\n", "      <td>29.59</td>\n", "      <td>9.02</td>\n", "      <td>2025-06-20</td>\n", "      <td>70</td>\n", "      <td>VCAMBF</td>\n", "      <td>VCAMBFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>VLGF</td>\n", "      <td>QUỸ ĐẦU TƯ TĂNG TRƯỞNG DÀI HẠN VIỆT NAM</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ SSI</td>\n", "      <td>1.50</td>\n", "      <td>2021-11-15</td>\n", "      <td>11859.99</td>\n", "      <td>-0.26</td>\n", "      <td>-3.70</td>\n", "      <td>18.62</td>\n", "      <td>...</td>\n", "      <td>-1.90</td>\n", "      <td>-2.09</td>\n", "      <td>-1.33</td>\n", "      <td>33.70</td>\n", "      <td>27.58</td>\n", "      <td>8.46</td>\n", "      <td>2025-06-20</td>\n", "      <td>49</td>\n", "      <td>VLGF</td>\n", "      <td>VLGFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>BVFED</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU NĂNG ĐỘNG BẢO VIỆT</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ BẢO VIỆT</td>\n", "      <td>1.00</td>\n", "      <td>2014-01-07</td>\n", "      <td>24422.00</td>\n", "      <td>0.31</td>\n", "      <td>6.78</td>\n", "      <td>116.99</td>\n", "      <td>...</td>\n", "      <td>2.82</td>\n", "      <td>9.20</td>\n", "      <td>9.81</td>\n", "      <td>31.10</td>\n", "      <td>27.22</td>\n", "      <td>8.36</td>\n", "      <td>2025-06-20</td>\n", "      <td>12</td>\n", "      <td>BVFED</td>\n", "      <td>BVFEDN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>VCBF-FIF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU VCBF</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN VI...</td>\n", "      <td>0.50</td>\n", "      <td>2019-08-08</td>\n", "      <td>14840.85</td>\n", "      <td>-0.08</td>\n", "      <td>2.60</td>\n", "      <td>48.41</td>\n", "      <td>...</td>\n", "      <td>0.98</td>\n", "      <td>2.88</td>\n", "      <td>6.24</td>\n", "      <td>15.68</td>\n", "      <td>25.40</td>\n", "      <td>7.84</td>\n", "      <td>2025-06-20</td>\n", "      <td>33</td>\n", "      <td>VCBFFIF</td>\n", "      <td>VCBFFIFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>VESAF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU TIẾP CẬN THỊ TRƯỜNG VINACA...</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL</td>\n", "      <td>1.75</td>\n", "      <td>2017-04-17</td>\n", "      <td>30689.22</td>\n", "      <td>0.14</td>\n", "      <td>-2.62</td>\n", "      <td>206.89</td>\n", "      <td>...</td>\n", "      <td>-0.42</td>\n", "      <td>-1.35</td>\n", "      <td>-2.54</td>\n", "      <td>31.79</td>\n", "      <td>25.12</td>\n", "      <td>7.76</td>\n", "      <td>2025-06-20</td>\n", "      <td>23</td>\n", "      <td>VESAF</td>\n", "      <td>VESAFN002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>VNDBF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU VND</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY TNHH MTV QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁ...</td>\n", "      <td>1.10</td>\n", "      <td>2019-07-04</td>\n", "      <td>14962.16</td>\n", "      <td>0.02</td>\n", "      <td>2.88</td>\n", "      <td>49.62</td>\n", "      <td>...</td>\n", "      <td>1.43</td>\n", "      <td>3.28</td>\n", "      <td>6.96</td>\n", "      <td>15.74</td>\n", "      <td>24.75</td>\n", "      <td>7.65</td>\n", "      <td>2025-06-20</td>\n", "      <td>37</td>\n", "      <td>VNDBF</td>\n", "      <td>VNDBFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>MAFF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU LINH HOẠT MIRAE ASSET VI...</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ MIREA ASSET VIỆT NAM</td>\n", "      <td>0.80</td>\n", "      <td>2021-12-16</td>\n", "      <td>12846.90</td>\n", "      <td>0.10</td>\n", "      <td>2.70</td>\n", "      <td>28.47</td>\n", "      <td>...</td>\n", "      <td>1.30</td>\n", "      <td>3.11</td>\n", "      <td>7.05</td>\n", "      <td>15.60</td>\n", "      <td>24.72</td>\n", "      <td>7.64</td>\n", "      <td>2025-06-20</td>\n", "      <td>50</td>\n", "      <td>MAFF</td>\n", "      <td>MAFFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>PVBF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU PVCOM</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ NGÂN HÀNG TMCP ĐẠI...</td>\n", "      <td>0.50</td>\n", "      <td>2020-02-06</td>\n", "      <td>14825.48</td>\n", "      <td>0.29</td>\n", "      <td>1.38</td>\n", "      <td>48.25</td>\n", "      <td>...</td>\n", "      <td>1.61</td>\n", "      <td>3.24</td>\n", "      <td>5.04</td>\n", "      <td>16.23</td>\n", "      <td>24.61</td>\n", "      <td>7.61</td>\n", "      <td>2025-06-20</td>\n", "      <td>45</td>\n", "      <td>PVBF</td>\n", "      <td>PVBFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>ENF</td>\n", "      <td>QUỸ ĐẦU TƯ NĂNG ĐỘNG EASTSPRING INVESTMENTS VI...</td>\n", "      <td><PERSON><PERSON><PERSON> cân bằng</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ EASTSPRING INVESTMENTS</td>\n", "      <td>1.50</td>\n", "      <td>2014-03-03</td>\n", "      <td>36957.00</td>\n", "      <td>0.24</td>\n", "      <td>-0.87</td>\n", "      <td>269.57</td>\n", "      <td>...</td>\n", "      <td>-1.52</td>\n", "      <td>0.29</td>\n", "      <td>-0.11</td>\n", "      <td>29.27</td>\n", "      <td>23.76</td>\n", "      <td>7.36</td>\n", "      <td>2025-06-20</td>\n", "      <td>81</td>\n", "      <td>ENF</td>\n", "      <td>ENFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>VFF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU BẢO THỊNH VINACAPITAL</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL</td>\n", "      <td>0.95</td>\n", "      <td>2013-03-31</td>\n", "      <td>24553.65</td>\n", "      <td>0.04</td>\n", "      <td>3.01</td>\n", "      <td>84.84</td>\n", "      <td>...</td>\n", "      <td>1.63</td>\n", "      <td>3.35</td>\n", "      <td>6.87</td>\n", "      <td>14.73</td>\n", "      <td>23.75</td>\n", "      <td>7.36</td>\n", "      <td>2025-06-20</td>\n", "      <td>21</td>\n", "      <td>VFF</td>\n", "      <td>VFFN003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>VIBF</td>\n", "      <td>QUỸ ĐẦU TƯ CÂN BẰNG TUỆ SÁNG VINACAPITAL</td>\n", "      <td><PERSON><PERSON><PERSON> cân bằng</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL</td>\n", "      <td>1.75</td>\n", "      <td>2019-07-01</td>\n", "      <td>18366.72</td>\n", "      <td>0.25</td>\n", "      <td>2.93</td>\n", "      <td>83.80</td>\n", "      <td>...</td>\n", "      <td>2.00</td>\n", "      <td>4.29</td>\n", "      <td>4.81</td>\n", "      <td>28.01</td>\n", "      <td>23.18</td>\n", "      <td>7.20</td>\n", "      <td>2025-06-20</td>\n", "      <td>22</td>\n", "      <td>VIBF</td>\n", "      <td>VIBFN003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>MBBOND</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU MB</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ MB</td>\n", "      <td>1.20</td>\n", "      <td>2018-03-25</td>\n", "      <td>15732.00</td>\n", "      <td>0.02</td>\n", "      <td>3.43</td>\n", "      <td>40.30</td>\n", "      <td>...</td>\n", "      <td>1.83</td>\n", "      <td>3.66</td>\n", "      <td>7.72</td>\n", "      <td>17.35</td>\n", "      <td>22.36</td>\n", "      <td>6.96</td>\n", "      <td>2025-06-20</td>\n", "      <td>48</td>\n", "      <td>MBBOND</td>\n", "      <td>MBBONDN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>VEOF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU HƯNG THỊNH VINACAPITAL</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL</td>\n", "      <td>1.75</td>\n", "      <td>2014-06-30</td>\n", "      <td>30097.10</td>\n", "      <td>0.27</td>\n", "      <td>-3.48</td>\n", "      <td>149.89</td>\n", "      <td>...</td>\n", "      <td>-3.34</td>\n", "      <td>-2.50</td>\n", "      <td>-1.33</td>\n", "      <td>31.19</td>\n", "      <td>21.91</td>\n", "      <td>6.83</td>\n", "      <td>2025-06-20</td>\n", "      <td>20</td>\n", "      <td>VEOF</td>\n", "      <td>VEOFN003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>ABBF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU AN BÌNH</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN...</td>\n", "      <td>1.45</td>\n", "      <td>2020-11-02</td>\n", "      <td>13477.06</td>\n", "      <td>0.00</td>\n", "      <td>2.78</td>\n", "      <td>34.77</td>\n", "      <td>...</td>\n", "      <td>1.46</td>\n", "      <td>2.98</td>\n", "      <td>5.83</td>\n", "      <td>12.55</td>\n", "      <td>21.54</td>\n", "      <td>6.72</td>\n", "      <td>2025-06-20</td>\n", "      <td>65</td>\n", "      <td>ABBF</td>\n", "      <td>ABBFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>DCBF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU DC</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ DRAGON CAPITAL VIỆ...</td>\n", "      <td>1.20</td>\n", "      <td>2013-06-09</td>\n", "      <td>27953.79</td>\n", "      <td>0.04</td>\n", "      <td>3.08</td>\n", "      <td>102.23</td>\n", "      <td>...</td>\n", "      <td>1.57</td>\n", "      <td>3.29</td>\n", "      <td>6.78</td>\n", "      <td>15.95</td>\n", "      <td>21.36</td>\n", "      <td>6.66</td>\n", "      <td>2025-06-20</td>\n", "      <td>27</td>\n", "      <td>VFMVFB</td>\n", "      <td>VFMVFBN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>BVBF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU BẢO VIỆT</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ BẢO VIỆT</td>\n", "      <td>0.50</td>\n", "      <td>2016-05-19</td>\n", "      <td>21142.00</td>\n", "      <td>0.00</td>\n", "      <td>2.13</td>\n", "      <td>36.87</td>\n", "      <td>...</td>\n", "      <td>1.26</td>\n", "      <td>1.95</td>\n", "      <td>6.09</td>\n", "      <td>15.66</td>\n", "      <td>21.31</td>\n", "      <td>6.65</td>\n", "      <td>2025-06-20</td>\n", "      <td>13</td>\n", "      <td>BVBF</td>\n", "      <td>BVBFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>SSIBF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU SSI</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ SSI</td>\n", "      <td>1.00</td>\n", "      <td>2017-08-29</td>\n", "      <td>16076.27</td>\n", "      <td>0.02</td>\n", "      <td>2.43</td>\n", "      <td>60.76</td>\n", "      <td>...</td>\n", "      <td>1.28</td>\n", "      <td>2.71</td>\n", "      <td>5.67</td>\n", "      <td>14.77</td>\n", "      <td>20.48</td>\n", "      <td>6.41</td>\n", "      <td>2025-06-20</td>\n", "      <td>8</td>\n", "      <td>SSIBF</td>\n", "      <td>SSIBFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>ASBF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU AN TOÀN AMBER</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ AMBER</td>\n", "      <td>0.85</td>\n", "      <td>2021-05-24</td>\n", "      <td>12292.97</td>\n", "      <td>0.01</td>\n", "      <td>2.32</td>\n", "      <td>23.10</td>\n", "      <td>...</td>\n", "      <td>1.18</td>\n", "      <td>2.51</td>\n", "      <td>5.18</td>\n", "      <td>12.21</td>\n", "      <td>18.83</td>\n", "      <td>5.92</td>\n", "      <td>2025-06-20</td>\n", "      <td>51</td>\n", "      <td>ASBF</td>\n", "      <td>ASBFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>MAGEF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU TĂNG TRƯỞNG MIRAE ASSET VI...</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ MIREA ASSET VIỆT NAM</td>\n", "      <td>1.75</td>\n", "      <td>2019-07-22</td>\n", "      <td>17036.20</td>\n", "      <td>0.25</td>\n", "      <td>-0.07</td>\n", "      <td>70.36</td>\n", "      <td>...</td>\n", "      <td>0.75</td>\n", "      <td>1.78</td>\n", "      <td>-1.27</td>\n", "      <td>29.44</td>\n", "      <td>17.58</td>\n", "      <td>5.55</td>\n", "      <td>2025-06-20</td>\n", "      <td>35</td>\n", "      <td>MAGEF</td>\n", "      <td>MAGEFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>MAFBAL</td>\n", "      <td>QUỸ ĐẦU TƯ CÂN BẰNG MANULIFE</td>\n", "      <td><PERSON><PERSON><PERSON> cân bằng</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ MANULIFE INVESTMENT (...</td>\n", "      <td>1.20</td>\n", "      <td>2017-09-28</td>\n", "      <td>16347.00</td>\n", "      <td>0.13</td>\n", "      <td>0.85</td>\n", "      <td>63.52</td>\n", "      <td>...</td>\n", "      <td>-0.29</td>\n", "      <td>1.41</td>\n", "      <td>1.49</td>\n", "      <td>20.53</td>\n", "      <td>17.30</td>\n", "      <td>5.46</td>\n", "      <td>2025-06-20</td>\n", "      <td>71</td>\n", "      <td>MAFBAL</td>\n", "      <td>MAFBAL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>VNDAF</td>\n", "      <td>QUỸ ĐẦU TƯ CHỦ ĐỘNG VND</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH MTV QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁ...</td>\n", "      <td>1.50</td>\n", "      <td>2018-01-11</td>\n", "      <td>15707.69</td>\n", "      <td>0.07</td>\n", "      <td>-2.23</td>\n", "      <td>57.08</td>\n", "      <td>...</td>\n", "      <td>-2.76</td>\n", "      <td>-1.15</td>\n", "      <td>-3.99</td>\n", "      <td>11.79</td>\n", "      <td>15.83</td>\n", "      <td>5.02</td>\n", "      <td>2025-06-20</td>\n", "      <td>38</td>\n", "      <td>VNDAF</td>\n", "      <td>VNDAFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>DCIP</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU GIA TĂNG THU NHẬP CỐ ĐỊN...</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ DRAGON CAPITAL VIỆ...</td>\n", "      <td>1.20</td>\n", "      <td>2021-05-23</td>\n", "      <td>11525.22</td>\n", "      <td>0.01</td>\n", "      <td>2.53</td>\n", "      <td>15.30</td>\n", "      <td>...</td>\n", "      <td>1.30</td>\n", "      <td>2.74</td>\n", "      <td>5.35</td>\n", "      <td>12.70</td>\n", "      <td>15.82</td>\n", "      <td>5.02</td>\n", "      <td>2025-06-20</td>\n", "      <td>67</td>\n", "      <td>VFMVFC</td>\n", "      <td>VFMVFCN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>VLBF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU THANH KHOẢN VINACAPITAL</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL</td>\n", "      <td>0.90</td>\n", "      <td>2021-08-12</td>\n", "      <td>11993.42</td>\n", "      <td>0.01</td>\n", "      <td>2.26</td>\n", "      <td>19.93</td>\n", "      <td>...</td>\n", "      <td>1.15</td>\n", "      <td>2.42</td>\n", "      <td>4.81</td>\n", "      <td>9.72</td>\n", "      <td>15.25</td>\n", "      <td>4.84</td>\n", "      <td>2025-06-20</td>\n", "      <td>53</td>\n", "      <td>VLBF</td>\n", "      <td>VLBFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>DFIX</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU DFVN</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY TNHH MTV QUẢN LÝ QUỸ DAI-ICHI LIFE VIỆ...</td>\n", "      <td>0.90</td>\n", "      <td>2021-02-03</td>\n", "      <td>11555.64</td>\n", "      <td>0.14</td>\n", "      <td>1.70</td>\n", "      <td>15.56</td>\n", "      <td>...</td>\n", "      <td>0.75</td>\n", "      <td>1.65</td>\n", "      <td>3.35</td>\n", "      <td>10.45</td>\n", "      <td>13.21</td>\n", "      <td>4.22</td>\n", "      <td>2025-06-20</td>\n", "      <td>30</td>\n", "      <td>DFIX</td>\n", "      <td>DFIXN002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>MAFEQI</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU MANULIFE</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ MANULIFE INVESTMENT (...</td>\n", "      <td>1.75</td>\n", "      <td>2014-09-30</td>\n", "      <td>17674.00</td>\n", "      <td>0.16</td>\n", "      <td>-2.11</td>\n", "      <td>76.76</td>\n", "      <td>...</td>\n", "      <td>-2.68</td>\n", "      <td>-1.19</td>\n", "      <td>-2.78</td>\n", "      <td>26.07</td>\n", "      <td>12.92</td>\n", "      <td>4.13</td>\n", "      <td>2025-06-20</td>\n", "      <td>72</td>\n", "      <td>MAFEQI</td>\n", "      <td>MAFEQI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>DCAF</td>\n", "      <td>QUỸ ĐẦU TƯ TĂNG TRƯỞNG DFVN</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH MTV QUẢN LÝ QUỸ DAI-ICHI LIFE VIỆ...</td>\n", "      <td>1.50</td>\n", "      <td>2019-01-02</td>\n", "      <td>15551.60</td>\n", "      <td>3.59</td>\n", "      <td>-3.47</td>\n", "      <td>55.52</td>\n", "      <td>...</td>\n", "      <td>-3.75</td>\n", "      <td>-2.75</td>\n", "      <td>-5.60</td>\n", "      <td>18.94</td>\n", "      <td>10.56</td>\n", "      <td>3.40</td>\n", "      <td>2025-06-20</td>\n", "      <td>29</td>\n", "      <td>DCAF</td>\n", "      <td>DCAFN002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>TBLF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU TĂNG TRƯỞNG BALLAD VIỆT NAM</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ SGI</td>\n", "      <td>1.30</td>\n", "      <td>2021-09-29</td>\n", "      <td>9685.47</td>\n", "      <td>3.68</td>\n", "      <td>2.28</td>\n", "      <td>-3.15</td>\n", "      <td>...</td>\n", "      <td>3.11</td>\n", "      <td>2.13</td>\n", "      <td>4.45</td>\n", "      <td>27.30</td>\n", "      <td>9.47</td>\n", "      <td>3.06</td>\n", "      <td>2025-06-20</td>\n", "      <td>41</td>\n", "      <td>TBLF</td>\n", "      <td>TBLFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>BMFF</td>\n", "      <td>QUỸ ĐẦU TƯ TĂNG TRƯỞNG BORDIER - MB FLAGSHIP</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ MB</td>\n", "      <td>1.80</td>\n", "      <td>2023-01-02</td>\n", "      <td>14127.00</td>\n", "      <td>0.63</td>\n", "      <td>10.81</td>\n", "      <td>41.27</td>\n", "      <td>...</td>\n", "      <td>6.10</td>\n", "      <td>13.20</td>\n", "      <td>13.04</td>\n", "      <td>31.21</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>87</td>\n", "      <td>BMFF</td>\n", "      <td>BMFFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>VCAMDF</td>\n", "      <td>QUỸ ĐẦU TƯ BẢN VIỆT DISCOVERY</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN...</td>\n", "      <td>1.50</td>\n", "      <td>NaN</td>\n", "      <td>10129.61</td>\n", "      <td>-0.56</td>\n", "      <td>4.07</td>\n", "      <td>1.30</td>\n", "      <td>...</td>\n", "      <td>8.72</td>\n", "      <td>4.60</td>\n", "      <td>0.19</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>75</td>\n", "      <td>VCAMDF</td>\n", "      <td>VCAMDFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>TCGF</td>\n", "      <td>QUỸ ĐẦU TƯ TĂNG TRƯỞNG THÀNH CÔNG</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ THÀNH CÔNG</td>\n", "      <td>1.00</td>\n", "      <td>NaN</td>\n", "      <td>10475.33</td>\n", "      <td>0.22</td>\n", "      <td>3.81</td>\n", "      <td>4.75</td>\n", "      <td>...</td>\n", "      <td>0.65</td>\n", "      <td>4.04</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>79</td>\n", "      <td>TCGF</td>\n", "      <td>TCGFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>LHBF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU LIGHTHOUSE</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ LIGHTHOUSE</td>\n", "      <td>1.00</td>\n", "      <td>2022-12-28</td>\n", "      <td>14206.46</td>\n", "      <td>0.14</td>\n", "      <td>3.75</td>\n", "      <td>39.74</td>\n", "      <td>...</td>\n", "      <td>0.28</td>\n", "      <td>3.79</td>\n", "      <td>11.23</td>\n", "      <td>26.68</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>64</td>\n", "      <td>LHBF</td>\n", "      <td>LHBFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>VCAMFI</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU BẢN VIỆT</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN...</td>\n", "      <td>1.00</td>\n", "      <td>2022-09-21</td>\n", "      <td>11840.26</td>\n", "      <td>-0.14</td>\n", "      <td>2.98</td>\n", "      <td>18.37</td>\n", "      <td>...</td>\n", "      <td>2.27</td>\n", "      <td>3.37</td>\n", "      <td>6.07</td>\n", "      <td>13.52</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>63</td>\n", "      <td>VCAMFI</td>\n", "      <td>VCAMFIN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>MBAM</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU DÒNG TIỀN LINH HOẠT MB</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ MB</td>\n", "      <td>1.50</td>\n", "      <td>2024-04-10</td>\n", "      <td>10711.00</td>\n", "      <td>0.06</td>\n", "      <td>3.03</td>\n", "      <td>7.11</td>\n", "      <td>...</td>\n", "      <td>1.74</td>\n", "      <td>3.20</td>\n", "      <td>6.44</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>88</td>\n", "      <td>MBAM</td>\n", "      <td>MBAMN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>VNDCF</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU LINH HOẠT VND</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY TNHH MTV QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁ...</td>\n", "      <td>0.90</td>\n", "      <td>2023-08-03</td>\n", "      <td>11044.13</td>\n", "      <td>0.03</td>\n", "      <td>2.48</td>\n", "      <td>10.44</td>\n", "      <td>...</td>\n", "      <td>1.39</td>\n", "      <td>2.60</td>\n", "      <td>5.34</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>40</td>\n", "      <td>VNDCF</td>\n", "      <td>VNDCFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>HDBOND</td>\n", "      <td>QUỸ ĐẦU TƯ TRÁI PHIẾU LỢI TỨC CAO HD</td>\n", "      <td><PERSON><PERSON><PERSON> tr<PERSON> p<PERSON></td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ HD</td>\n", "      <td>0.95</td>\n", "      <td>2022-12-29</td>\n", "      <td>11610.62</td>\n", "      <td>-0.06</td>\n", "      <td>1.90</td>\n", "      <td>16.12</td>\n", "      <td>...</td>\n", "      <td>0.62</td>\n", "      <td>0.90</td>\n", "      <td>2.82</td>\n", "      <td>11.79</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>62</td>\n", "      <td>HDBOND</td>\n", "      <td>HDBONDN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>UVDIF</td>\n", "      <td>QUỸ ĐẦU TƯ UNITED THU NHẬP NĂNG ĐỘNG VIỆT NAM</td>\n", "      <td><PERSON><PERSON><PERSON> cân bằng</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ UOB ASSET MANAGEME...</td>\n", "      <td>1.50</td>\n", "      <td>NaN</td>\n", "      <td>10148.98</td>\n", "      <td>0.06</td>\n", "      <td>0.12</td>\n", "      <td>1.49</td>\n", "      <td>...</td>\n", "      <td>0.83</td>\n", "      <td>0.87</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>78</td>\n", "      <td>UVDIF</td>\n", "      <td>UVDIFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>LHCDF</td>\n", "      <td>QUỸ ĐẦU TƯ NĂNG ĐỘNG LIGHTHOUSE</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ LIGHTHOUSE</td>\n", "      <td>1.50</td>\n", "      <td>NaN</td>\n", "      <td>11014.91</td>\n", "      <td>0.18</td>\n", "      <td>-0.30</td>\n", "      <td>10.15</td>\n", "      <td>...</td>\n", "      <td>-1.73</td>\n", "      <td>0.67</td>\n", "      <td>10.09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>76</td>\n", "      <td>LHCDF</td>\n", "      <td>LHCDFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>UVEEF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU UNITED ESG VIỆT NAM</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ UOB ASSET MANAGEME...</td>\n", "      <td>1.75</td>\n", "      <td>2022-09-29</td>\n", "      <td>14535.88</td>\n", "      <td>0.23</td>\n", "      <td>-2.09</td>\n", "      <td>45.36</td>\n", "      <td>...</td>\n", "      <td>0.10</td>\n", "      <td>0.20</td>\n", "      <td>-2.27</td>\n", "      <td>24.45</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>58</td>\n", "      <td>UVEEF</td>\n", "      <td>UVEEFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>PHVSF</td>\n", "      <td>QUỸ ĐẦU TƯ CHỌN LỌC PHÚ HƯNG VIỆT NAM</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ PHÚ HƯNG</td>\n", "      <td>1.50</td>\n", "      <td>2022-11-20</td>\n", "      <td>11903.79</td>\n", "      <td>0.03</td>\n", "      <td>-2.90</td>\n", "      <td>19.05</td>\n", "      <td>...</td>\n", "      <td>-1.53</td>\n", "      <td>-0.72</td>\n", "      <td>-4.71</td>\n", "      <td>15.33</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>66</td>\n", "      <td>PHVSF</td>\n", "      <td>PHVSFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>MDI</td>\n", "      <td>QUỸ ĐẦU TƯ NĂNG ĐỘNG MANULIFE</td>\n", "      <td><PERSON><PERSON><PERSON> cân bằng</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ MANULIFE INVESTMENT (...</td>\n", "      <td>1.75</td>\n", "      <td>2023-11-29</td>\n", "      <td>10897.00</td>\n", "      <td>0.08</td>\n", "      <td>-1.39</td>\n", "      <td>8.98</td>\n", "      <td>...</td>\n", "      <td>-2.10</td>\n", "      <td>-0.81</td>\n", "      <td>0.37</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>77</td>\n", "      <td>MDI</td>\n", "      <td>MDI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>GFM-VIF</td>\n", "      <td>QUỸ ĐẦU TƯ GIA TĂNG GIÁ TRỊ GFM</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY QUẢN LÝ QUỸ GENESIS</td>\n", "      <td>1.50</td>\n", "      <td>NaN</td>\n", "      <td>9980.75</td>\n", "      <td>0.01</td>\n", "      <td>-3.42</td>\n", "      <td>-0.19</td>\n", "      <td>...</td>\n", "      <td>-5.58</td>\n", "      <td>-1.60</td>\n", "      <td>-3.59</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>69</td>\n", "      <td>GFMVIF</td>\n", "      <td>GFMVIFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>VMEEF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU KINH TẾ HIỆN ĐẠI VINACAPITAL</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL</td>\n", "      <td>1.75</td>\n", "      <td>2023-04-10</td>\n", "      <td>14760.61</td>\n", "      <td>0.14</td>\n", "      <td>-2.60</td>\n", "      <td>47.65</td>\n", "      <td>...</td>\n", "      <td>-1.82</td>\n", "      <td>-1.77</td>\n", "      <td>-2.19</td>\n", "      <td>41.77</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>68</td>\n", "      <td>VMPF</td>\n", "      <td>VMPFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>VDEF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU CỔ TỨC NĂNG ĐỘNG VINACAPITAL</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL</td>\n", "      <td>1.75</td>\n", "      <td>2024-06-03</td>\n", "      <td>9877.21</td>\n", "      <td>0.41</td>\n", "      <td>-6.52</td>\n", "      <td>-1.23</td>\n", "      <td>...</td>\n", "      <td>-8.47</td>\n", "      <td>-4.30</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>80</td>\n", "      <td>VDEF</td>\n", "      <td>VDEFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>PBIF</td>\n", "      <td>QUỸ ĐẦU TƯ CÂN BẰNG PVCOM</td>\n", "      <td><PERSON><PERSON><PERSON> cân bằng</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ NGÂN HÀNG TMCP ĐẠI...</td>\n", "      <td>1.30</td>\n", "      <td>NaN</td>\n", "      <td>9835.99</td>\n", "      <td>-12.02</td>\n", "      <td>-5.95</td>\n", "      <td>-1.64</td>\n", "      <td>...</td>\n", "      <td>-7.04</td>\n", "      <td>-5.64</td>\n", "      <td>-7.73</td>\n", "      <td>-1.90</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>61</td>\n", "      <td>PBIF</td>\n", "      <td>PBIFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>NTPPF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU TRIỂN VỌNG NTP</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ NTP</td>\n", "      <td>1.75</td>\n", "      <td>2022-06-11</td>\n", "      <td>9612.72</td>\n", "      <td>-0.07</td>\n", "      <td>-7.40</td>\n", "      <td>-3.78</td>\n", "      <td>...</td>\n", "      <td>-6.13</td>\n", "      <td>-5.73</td>\n", "      <td>-9.39</td>\n", "      <td>0.18</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>52</td>\n", "      <td>TVPF</td>\n", "      <td>TVPFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>KDEF</td>\n", "      <td>QUỸ ĐẦU TƯ CỔ PHIẾU CỔ TỨC TĂNG TRƯỞNG KIM</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ KIM VIỆT NAM</td>\n", "      <td>1.70</td>\n", "      <td>NaN</td>\n", "      <td>10817.72</td>\n", "      <td>0.53</td>\n", "      <td>NaN</td>\n", "      <td>8.18</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>86</td>\n", "      <td>KDEF</td>\n", "      <td>KDEFN001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>RVPIF</td>\n", "      <td>QUỸ ĐẦU TƯ THỊNH VƯỢNG RỒNG VIỆT</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ RỒNG VIỆT</td>\n", "      <td>1.50</td>\n", "      <td>NaN</td>\n", "      <td>10233.87</td>\n", "      <td>3.05</td>\n", "      <td>NaN</td>\n", "      <td>2.34</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>83</td>\n", "      <td>RVPF24</td>\n", "      <td>RVPF24N001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>VCBF-AIF</td>\n", "      <td>QUỸ ĐẦU TƯ THU NHẬP CHỦ ĐỘNG VCBF</td>\n", "      <td><PERSON><PERSON><PERSON> cổ phiếu</td>\n", "      <td>CÔNG TY TNHH QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN VI...</td>\n", "      <td>1.90</td>\n", "      <td>NaN</td>\n", "      <td>9865.99</td>\n", "      <td>0.09</td>\n", "      <td>NaN</td>\n", "      <td>-1.34</td>\n", "      <td>...</td>\n", "      <td>-0.42</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2025-06-20</td>\n", "      <td>82</td>\n", "      <td>VCBFAIF</td>\n", "      <td>VCBFAIFN001</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>56 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   short_name                                               name  \\\n", "0        DCDS                QUỸ ĐẦU TƯ CHỨNG KHOÁN NĂNG ĐỘNG DC   \n", "1        MBVF                      QUỸ ĐẦU TƯ GIÁ TRỊ MB CAPITAL   \n", "2      SSISCA         QUỸ ĐẦU TƯ LỢI THẾ CẠNH TRANH BỀN VỮNG SSI   \n", "3    VCBF-MGF               QUỸ ĐẦU TƯ CỔ PHIẾU TĂNG TRƯỞNG VCBF   \n", "4        BVPF            QUỸ ĐẦU TƯ CỔ PHIẾU TRIỂN VỌNG BẢO VIỆT   \n", "5    VCBF-BCF                  QUỸ ĐẦU TƯ CỔ PHIẾU HÀNG ĐẦU VCBF   \n", "6        DCDE            QUỸ ĐẦU TƯ CỔ PHIẾU TẬP TRUNG CỔ TỨC DC   \n", "7    VCBF-TBF                QUỸ ĐẦU TƯ CÂN BẰNG CHIẾN LƯỢC VCBF   \n", "8      VCAMBF                       QUỸ ĐẦU TƯ CÂN BẰNG BẢN VIỆT   \n", "9        VLGF            QUỸ ĐẦU TƯ TĂNG TRƯỞNG DÀI HẠN VIỆT NAM   \n", "10      BVFED             QUỸ ĐẦU TƯ CỔ PHIẾU NĂNG ĐỘNG BẢO VIỆT   \n", "11   VCBF-FIF                         QUỸ ĐẦU TƯ TRÁI PHIẾU VCBF   \n", "12      VESAF  QUỸ ĐẦU TƯ CỔ PHIẾU TIẾP CẬN THỊ TRƯỜNG VINACA...   \n", "13      VNDBF                          QUỸ ĐẦU TƯ TRÁI PHIẾU VND   \n", "14       MAFF  QUỸ ĐẦU TƯ TRÁI PHIẾU LINH HOẠT MIRAE ASSET VI...   \n", "15       PVBF                        QUỸ ĐẦU TƯ TRÁI PHIẾU PVCOM   \n", "16        ENF  QUỸ ĐẦU TƯ NĂNG ĐỘNG EASTSPRING INVESTMENTS VI...   \n", "17        VFF        QUỸ ĐẦU TƯ TRÁI PHIẾU BẢO THỊNH VINACAPITAL   \n", "18       VIBF           QUỸ ĐẦU TƯ CÂN BẰNG TUỆ SÁNG VINACAPITAL   \n", "19     MBBOND                           QUỸ ĐẦU TƯ TRÁI PHIẾU MB   \n", "20       VEOF         QUỸ ĐẦU TƯ CỔ PHIẾU HƯNG THỊNH VINACAPITAL   \n", "21       ABBF                      QUỸ ĐẦU TƯ TRÁI PHIẾU AN BÌNH   \n", "22       DCBF                           QUỸ ĐẦU TƯ TRÁI PHIẾU DC   \n", "23       BVBF                    QUỸ ĐẦU TƯ TRÁI PHIẾU BẢO VIỆT    \n", "24      SSIBF                          QUỸ ĐẦU TƯ TRÁI PHIẾU SSI   \n", "25       ASBF                QUỸ ĐẦU TƯ TRÁI PHIẾU AN TOÀN AMBER   \n", "26      MAGEF  QUỸ ĐẦU TƯ CỔ PHIẾU TĂNG TRƯỞNG MIRAE ASSET VI...   \n", "27     MAFBAL                       QUỸ ĐẦU TƯ CÂN BẰNG MANULIFE   \n", "28      VNDAF                            QUỸ ĐẦU TƯ CHỦ ĐỘNG VND   \n", "29       DCIP  QUỸ ĐẦU TƯ TRÁI PHIẾU GIA TĂNG THU NHẬP CỐ ĐỊN...   \n", "30       VLBF      QUỸ ĐẦU TƯ TRÁI PHIẾU THANH KHOẢN VINACAPITAL   \n", "31       DFIX                         QUỸ ĐẦU TƯ TRÁI PHIẾU DFVN   \n", "32     MAFEQI                       QUỸ ĐẦU TƯ CỔ PHIẾU MANULIFE   \n", "33       DCAF                        QUỸ ĐẦU TƯ TĂNG TRƯỞNG DFVN   \n", "34       TBLF    QUỸ ĐẦU TƯ CỔ PHIẾU TĂNG TRƯỞNG BALLAD VIỆT NAM   \n", "35       BMFF       QUỸ ĐẦU TƯ TĂNG TRƯỞNG BORDIER - MB FLAGSHIP   \n", "36     VCAMDF                      QUỸ ĐẦU TƯ BẢN VIỆT DISCOVERY   \n", "37       TCGF                  QUỸ ĐẦU TƯ TĂNG TRƯỞNG THÀNH CÔNG   \n", "38       LHBF                   QUỸ ĐẦU TƯ TRÁI PHIẾU LIGHTHOUSE   \n", "39     VCAMFI                     QUỸ ĐẦU TƯ TRÁI PHIẾU BẢN VIỆT   \n", "40       MBAM       QUỸ ĐẦU TƯ TRÁI PHIẾU DÒNG TIỀN LINH HOẠT MB   \n", "41      VNDCF                QUỸ ĐẦU TƯ TRÁI PHIẾU LINH HOẠT VND   \n", "42     HDBOND               QUỸ ĐẦU TƯ TRÁI PHIẾU LỢI TỨC CAO HD   \n", "43      UVDIF      QUỸ ĐẦU TƯ UNITED THU NHẬP NĂNG ĐỘNG VIỆT NAM   \n", "44      LHCDF                    QUỸ ĐẦU TƯ NĂNG ĐỘNG LIGHTHOUSE   \n", "45      UVEEF            QUỸ ĐẦU TƯ CỔ PHIẾU UNITED ESG VIỆT NAM   \n", "46      PHVSF              QUỸ ĐẦU TƯ CHỌN LỌC PHÚ HƯNG VIỆT NAM   \n", "47        MDI                      QUỸ ĐẦU TƯ NĂNG ĐỘNG MANULIFE   \n", "48    GFM-VIF                    QUỸ ĐẦU TƯ GIA TĂNG GIÁ TRỊ GFM   \n", "49      VMEEF   QUỸ ĐẦU TƯ CỔ PHIẾU KINH TẾ HIỆN ĐẠI VINACAPITAL   \n", "50       VDEF   QUỸ ĐẦU TƯ CỔ PHIẾU CỔ TỨC NĂNG ĐỘNG VINACAPITAL   \n", "51       PBIF                          QUỸ ĐẦU TƯ CÂN BẰNG PVCOM   \n", "52      NTPPF                 QUỸ ĐẦU TƯ CỔ PHIẾU TRIỂN VỌNG NTP   \n", "53       KDEF         QUỸ ĐẦU TƯ CỔ PHIẾU CỔ TỨC TĂNG TRƯỞNG KIM   \n", "54      RVPIF                   QUỸ ĐẦU TƯ THỊNH VƯỢNG RỒNG VIỆT   \n", "55   VCBF-AIF                  QUỸ ĐẦU TƯ THU NHẬP CHỦ ĐỘNG VCBF   \n", "\n", "         fund_type                                    fund_owner_name  \\\n", "0     Quỹ cổ phiếu  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ DRAGON CAPITAL VIỆ...   \n", "1     Quỹ cổ phiếu              CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ MB   \n", "2     Quỹ cổ phiếu                       CÔNG TY TNHH QUẢN LÝ QUỸ SSI   \n", "3     Quỹ cổ phiếu  CÔNG TY TNHH QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN VI...   \n", "4     Quỹ cổ phiếu                  CÔNG TY TNHH QUẢN LÝ QUỸ BẢO VIỆT   \n", "5     Quỹ cổ phiếu  CÔNG TY TNHH QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN VI...   \n", "6     Quỹ cổ phiếu  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ DRAGON CAPITAL VIỆ...   \n", "7     Quỹ cân bằng  CÔNG TY TNHH QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN VI...   \n", "8     Quỹ cân bằng  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN...   \n", "9     Quỹ cổ phiếu                       CÔNG TY TNHH QUẢN LÝ QUỸ SSI   \n", "10    Quỹ cổ phiếu                  CÔNG TY TNHH QUẢN LÝ QUỸ BẢO VIỆT   \n", "11  Quỹ trái phiếu  CÔNG TY TNHH QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN VI...   \n", "12    Quỹ cổ phiếu            CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL   \n", "13  Quỹ trái phiếu  CÔNG TY TNHH MTV QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁ...   \n", "14  Quỹ trái phiếu      CÔNG TY TNHH QUẢN LÝ QUỸ MIREA ASSET VIỆT NAM   \n", "15  Quỹ trái phiếu  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ NGÂN HÀNG TMCP ĐẠI...   \n", "16    Quỹ cân bằng    CÔNG TY TNHH QUẢN LÝ QUỸ EASTSPRING INVESTMENTS   \n", "17  Quỹ trái phiếu            CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL   \n", "18    Quỹ cân bằng            CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL   \n", "19  Quỹ trái phiếu              CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ MB   \n", "20    Quỹ cổ phiếu            CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL   \n", "21  Quỹ trái phiếu  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN...   \n", "22  Quỹ trái phiếu  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ DRAGON CAPITAL VIỆ...   \n", "23  Quỹ trái phiếu                  CÔNG TY TNHH QUẢN LÝ QUỸ BẢO VIỆT   \n", "24  Quỹ trái phiếu                       CÔNG TY TNHH QUẢN LÝ QUỸ SSI   \n", "25  Quỹ trái phiếu                  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ AMBER   \n", "26    Quỹ cổ phiếu      CÔNG TY TNHH QUẢN LÝ QUỸ MIREA ASSET VIỆT NAM   \n", "27    Quỹ cân bằng  CÔNG TY TNHH QUẢN LÝ QUỸ MANULIFE INVESTMENT (...   \n", "28    Q<PERSON>ỹ cổ phiếu  CÔNG TY TNHH MTV QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁ...   \n", "29  Quỹ trái phiếu  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ DRAGON CAPITAL VIỆ...   \n", "30  Quỹ trái phiếu            CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL   \n", "31  Qu<PERSON> tr<PERSON>i phiếu  CÔNG TY TNHH MTV QUẢN LÝ QUỸ DAI-ICHI LIFE VIỆ...   \n", "32    <PERSON><PERSON><PERSON> cổ phiếu  CÔNG TY TNHH QUẢN LÝ QUỸ MANULIFE INVESTMENT (...   \n", "33    <PERSON><PERSON><PERSON> cổ phiếu  CÔNG TY TNHH MTV QUẢN LÝ QUỸ DAI-ICHI LIFE VIỆ...   \n", "34    Quỹ cổ phiếu             CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ SGI   \n", "35    Quỹ cổ phiếu              CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ MB   \n", "36    Quỹ cổ phiếu  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN...   \n", "37    Quỹ cổ phiếu                CÔNG TY TNHH QUẢN LÝ QUỸ THÀNH CÔNG   \n", "38  Quỹ trái phiếu      CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ LIGHTHOUSE   \n", "39  Quỹ trái phiếu  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN...   \n", "40  Quỹ trái phiếu              CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ MB   \n", "41  Quỹ trái phiếu  CÔNG TY TNHH MTV QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁ...   \n", "42  Quỹ trái phiếu                     CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ HD   \n", "43    Quỹ cân bằng  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ UOB ASSET MANAGEME...   \n", "44    Quỹ cổ phiếu      CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ ĐẦU TƯ LIGHTHOUSE   \n", "45    Quỹ cổ phiếu  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ UOB ASSET MANAGEME...   \n", "46    Quỹ cổ phiếu               CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ PHÚ HƯNG   \n", "47    Quỹ cân bằng  CÔNG TY TNHH QUẢN LÝ QUỸ MANULIFE INVESTMENT (...   \n", "48    Quỹ cổ phiếu                        CÔNG TY QUẢN LÝ QUỸ GENESIS   \n", "49    Quỹ cổ phiếu            CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL   \n", "50    Quỹ cổ phiếu            CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ VINACAPITAL   \n", "51    Quỹ cân bằng  CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ NGÂN HÀNG TMCP ĐẠI...   \n", "52    Quỹ cổ phiếu                    CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ NTP   \n", "53    Quỹ cổ phiếu              CÔNG TY TNHH QUẢN LÝ QUỸ KIM VIỆT NAM   \n", "54    Quỹ cổ phiếu              CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ RỒNG VIỆT   \n", "55    Quỹ cổ phiếu  CÔNG TY TNHH QUẢN LÝ QUỸ ĐẦU TƯ CHỨNG KHOÁN VI...   \n", "\n", "    management_fee inception_date       nav  nav_change_previous  \\\n", "0             1.95     2004-05-19  87007.21                 0.44   \n", "1             1.50     2014-04-24  24375.00                 0.63   \n", "2             1.75     2014-09-25  40380.65                 0.32   \n", "3             1.90     2021-10-28  13372.16                 0.13   \n", "4             1.50     2016-12-28  20832.00                 0.30   \n", "5             1.90     2014-08-21  36573.99                 0.19   \n", "6             1.93     2008-02-27  27778.39                 0.31   \n", "7             1.50     2013-12-23  33858.91                 0.20   \n", "8             1.20     2014-04-10  19309.99                -0.12   \n", "9             1.50     2021-11-15  11859.99                -0.26   \n", "10            1.00     2014-01-07  24422.00                 0.31   \n", "11            0.50     2019-08-08  14840.85                -0.08   \n", "12            1.75     2017-04-17  30689.22                 0.14   \n", "13            1.10     2019-07-04  14962.16                 0.02   \n", "14            0.80     2021-12-16  12846.90                 0.10   \n", "15            0.50     2020-02-06  14825.48                 0.29   \n", "16            1.50     2014-03-03  36957.00                 0.24   \n", "17            0.95     2013-03-31  24553.65                 0.04   \n", "18            1.75     2019-07-01  18366.72                 0.25   \n", "19            1.20     2018-03-25  15732.00                 0.02   \n", "20            1.75     2014-06-30  30097.10                 0.27   \n", "21            1.45     2020-11-02  13477.06                 0.00   \n", "22            1.20     2013-06-09  27953.79                 0.04   \n", "23            0.50     2016-05-19  21142.00                 0.00   \n", "24            1.00     2017-08-29  16076.27                 0.02   \n", "25            0.85     2021-05-24  12292.97                 0.01   \n", "26            1.75     2019-07-22  17036.20                 0.25   \n", "27            1.20     2017-09-28  16347.00                 0.13   \n", "28            1.50     2018-01-11  15707.69                 0.07   \n", "29            1.20     2021-05-23  11525.22                 0.01   \n", "30            0.90     2021-08-12  11993.42                 0.01   \n", "31            0.90     2021-02-03  11555.64                 0.14   \n", "32            1.75     2014-09-30  17674.00                 0.16   \n", "33            1.50     2019-01-02  15551.60                 3.59   \n", "34            1.30     2021-09-29   9685.47                 3.68   \n", "35            1.80     2023-01-02  14127.00                 0.63   \n", "36            1.50            NaN  10129.61                -0.56   \n", "37            1.00            NaN  10475.33                 0.22   \n", "38            1.00     2022-12-28  14206.46                 0.14   \n", "39            1.00     2022-09-21  11840.26                -0.14   \n", "40            1.50     2024-04-10  10711.00                 0.06   \n", "41            0.90     2023-08-03  11044.13                 0.03   \n", "42            0.95     2022-12-29  11610.62                -0.06   \n", "43            1.50            NaN  10148.98                 0.06   \n", "44            1.50            NaN  11014.91                 0.18   \n", "45            1.75     2022-09-29  14535.88                 0.23   \n", "46            1.50     2022-11-20  11903.79                 0.03   \n", "47            1.75     2023-11-29  10897.00                 0.08   \n", "48            1.50            NaN   9980.75                 0.01   \n", "49            1.75     2023-04-10  14760.61                 0.14   \n", "50            1.75     2024-06-03   9877.21                 0.41   \n", "51            1.30            NaN   9835.99               -12.02   \n", "52            1.75     2022-06-11   9612.72                -0.07   \n", "53            1.70            NaN  10817.72                 0.53   \n", "54            1.50            NaN  10233.87                 3.05   \n", "55            1.90            NaN   9865.99                 0.09   \n", "\n", "    nav_change_last_year  nav_change_inception  ...  nav_change_3m  \\\n", "0                   6.60                209.12  ...           8.47   \n", "1                   9.62                 70.61  ...           5.11   \n", "2                  -0.99                180.18  ...          -1.44   \n", "3                  -0.46                 33.72  ...          -0.44   \n", "4                  -2.38                108.28  ...          -1.62   \n", "5                   2.05                 87.12  ...           2.83   \n", "6                   2.32                143.35  ...           4.18   \n", "7                   1.85                 69.75  ...           2.05   \n", "8                   2.43                 62.02  ...           2.84   \n", "9                  -3.70                 18.62  ...          -1.90   \n", "10                  6.78                116.99  ...           2.82   \n", "11                  2.60                 48.41  ...           0.98   \n", "12                 -2.62                206.89  ...          -0.42   \n", "13                  2.88                 49.62  ...           1.43   \n", "14                  2.70                 28.47  ...           1.30   \n", "15                  1.38                 48.25  ...           1.61   \n", "16                 -0.87                269.57  ...          -1.52   \n", "17                  3.01                 84.84  ...           1.63   \n", "18                  2.93                 83.80  ...           2.00   \n", "19                  3.43                 40.30  ...           1.83   \n", "20                 -3.48                149.89  ...          -3.34   \n", "21                  2.78                 34.77  ...           1.46   \n", "22                  3.08                102.23  ...           1.57   \n", "23                  2.13                 36.87  ...           1.26   \n", "24                  2.43                 60.76  ...           1.28   \n", "25                  2.32                 23.10  ...           1.18   \n", "26                 -0.07                 70.36  ...           0.75   \n", "27                  0.85                 63.52  ...          -0.29   \n", "28                 -2.23                 57.08  ...          -2.76   \n", "29                  2.53                 15.30  ...           1.30   \n", "30                  2.26                 19.93  ...           1.15   \n", "31                  1.70                 15.56  ...           0.75   \n", "32                 -2.11                 76.76  ...          -2.68   \n", "33                 -3.47                 55.52  ...          -3.75   \n", "34                  2.28                 -3.15  ...           3.11   \n", "35                 10.81                 41.27  ...           6.10   \n", "36                  4.07                  1.30  ...           8.72   \n", "37                  3.81                  4.75  ...           0.65   \n", "38                  3.75                 39.74  ...           0.28   \n", "39                  2.98                 18.37  ...           2.27   \n", "40                  3.03                  7.11  ...           1.74   \n", "41                  2.48                 10.44  ...           1.39   \n", "42                  1.90                 16.12  ...           0.62   \n", "43                  0.12                  1.49  ...           0.83   \n", "44                 -0.30                 10.15  ...          -1.73   \n", "45                 -2.09                 45.36  ...           0.10   \n", "46                 -2.90                 19.05  ...          -1.53   \n", "47                 -1.39                  8.98  ...          -2.10   \n", "48                 -3.42                 -0.19  ...          -5.58   \n", "49                 -2.60                 47.65  ...          -1.82   \n", "50                 -6.52                 -1.23  ...          -8.47   \n", "51                 -5.95                 -1.64  ...          -7.04   \n", "52                 -7.40                 -3.78  ...          -6.13   \n", "53                   NaN                  8.18  ...            NaN   \n", "54                   NaN                  2.34  ...            NaN   \n", "55                   NaN                 -1.34  ...          -0.42   \n", "\n", "    nav_change_6m  nav_change_12m  nav_change_24m  nav_change_36m  \\\n", "0            8.37            5.90           48.42           46.63   \n", "1           12.18           11.79           36.36           46.20   \n", "2            0.83            1.58           47.33           42.64   \n", "3            0.86            6.00           40.76           40.59   \n", "4           -0.97           -0.13           33.49           33.06   \n", "5            3.79            7.57           38.38           32.33   \n", "6            4.27            2.22           34.64           31.19   \n", "7            2.96            6.46           31.48           31.07   \n", "8            4.08            2.30           29.80           29.59   \n", "9           -2.09           -1.33           33.70           27.58   \n", "10           9.20            9.81           31.10           27.22   \n", "11           2.88            6.24           15.68           25.40   \n", "12          -1.35           -2.54           31.79           25.12   \n", "13           3.28            6.96           15.74           24.75   \n", "14           3.11            7.05           15.60           24.72   \n", "15           3.24            5.04           16.23           24.61   \n", "16           0.29           -0.11           29.27           23.76   \n", "17           3.35            6.87           14.73           23.75   \n", "18           4.29            4.81           28.01           23.18   \n", "19           3.66            7.72           17.35           22.36   \n", "20          -2.50           -1.33           31.19           21.91   \n", "21           2.98            5.83           12.55           21.54   \n", "22           3.29            6.78           15.95           21.36   \n", "23           1.95            6.09           15.66           21.31   \n", "24           2.71            5.67           14.77           20.48   \n", "25           2.51            5.18           12.21           18.83   \n", "26           1.78           -1.27           29.44           17.58   \n", "27           1.41            1.49           20.53           17.30   \n", "28          -1.15           -3.99           11.79           15.83   \n", "29           2.74            5.35           12.70           15.82   \n", "30           2.42            4.81            9.72           15.25   \n", "31           1.65            3.35           10.45           13.21   \n", "32          -1.19           -2.78           26.07           12.92   \n", "33          -2.75           -5.60           18.94           10.56   \n", "34           2.13            4.45           27.30            9.47   \n", "35          13.20           13.04           31.21             NaN   \n", "36           4.60            0.19             NaN             NaN   \n", "37           4.04             NaN             NaN             NaN   \n", "38           3.79           11.23           26.68             NaN   \n", "39           3.37            6.07           13.52             NaN   \n", "40           3.20            6.44             NaN             NaN   \n", "41           2.60            5.34             NaN             NaN   \n", "42           0.90            2.82           11.79             NaN   \n", "43           0.87             NaN             NaN             NaN   \n", "44           0.67           10.09             NaN             NaN   \n", "45           0.20           -2.27           24.45             NaN   \n", "46          -0.72           -4.71           15.33             NaN   \n", "47          -0.81            0.37             NaN             NaN   \n", "48          -1.60           -3.59             NaN             NaN   \n", "49          -1.77           -2.19           41.77             NaN   \n", "50          -4.30             NaN             NaN             NaN   \n", "51          -5.64           -7.73           -1.90             NaN   \n", "52          -5.73           -9.39            0.18             NaN   \n", "53            NaN             NaN             NaN             NaN   \n", "54            NaN             NaN             NaN             NaN   \n", "55            NaN             NaN             NaN             NaN   \n", "\n", "    nav_change_36m_annualized  nav_update_at fund_id_fmarket  fund_code  \\\n", "0                       13.61     2025-06-20              28     VFMVF1   \n", "1                       13.50     2025-06-20              47       MBVF   \n", "2                       12.57     2025-06-20              11     SSISCA   \n", "3                       12.03     2025-06-20              46    VCBFMGF   \n", "4                        9.99     2025-06-20              14       BVPF   \n", "5                        9.79     2025-06-20              32    VCBFBCF   \n", "6                        9.47     2025-06-20              25     VFMVF4   \n", "7                        9.44     2025-06-20              31    VCBFTBF   \n", "8                        9.02     2025-06-20              70     VCAMBF   \n", "9                        8.46     2025-06-20              49       VLGF   \n", "10                       8.36     2025-06-20              12      BVFED   \n", "11                       7.84     2025-06-20              33    VCBFFIF   \n", "12                       7.76     2025-06-20              23      VESAF   \n", "13                       7.65     2025-06-20              37      VNDBF   \n", "14                       7.64     2025-06-20              50       MAFF   \n", "15                       7.61     2025-06-20              45       PVBF   \n", "16                       7.36     2025-06-20              81        ENF   \n", "17                       7.36     2025-06-20              21        VFF   \n", "18                       7.20     2025-06-20              22       VIBF   \n", "19                       6.96     2025-06-20              48     MBBOND   \n", "20                       6.83     2025-06-20              20       VEOF   \n", "21                       6.72     2025-06-20              65       ABBF   \n", "22                       6.66     2025-06-20              27     VFMVFB   \n", "23                       6.65     2025-06-20              13       BVBF   \n", "24                       6.41     2025-06-20               8      SSIBF   \n", "25                       5.92     2025-06-20              51       ASBF   \n", "26                       5.55     2025-06-20              35      MAGEF   \n", "27                       5.46     2025-06-20              71     MAFBAL   \n", "28                       5.02     2025-06-20              38      VNDAF   \n", "29                       5.02     2025-06-20              67     VFMVFC   \n", "30                       4.84     2025-06-20              53       VLBF   \n", "31                       4.22     2025-06-20              30       DFIX   \n", "32                       4.13     2025-06-20              72     MAFEQI   \n", "33                       3.40     2025-06-20              29       DCAF   \n", "34                       3.06     2025-06-20              41       TBLF   \n", "35                        NaN     2025-06-20              87       BMFF   \n", "36                        NaN     2025-06-20              75     VCAMDF   \n", "37                        NaN     2025-06-20              79       TCGF   \n", "38                        NaN     2025-06-20              64       LHBF   \n", "39                        NaN     2025-06-20              63     VCAMFI   \n", "40                        NaN     2025-06-20              88       MBAM   \n", "41                        NaN     2025-06-20              40      VNDCF   \n", "42                        NaN     2025-06-20              62     HDBOND   \n", "43                        NaN     2025-06-20              78      UVDIF   \n", "44                        NaN     2025-06-20              76      LHCDF   \n", "45                        NaN     2025-06-20              58      UVEEF   \n", "46                        NaN     2025-06-20              66      PHVSF   \n", "47                        NaN     2025-06-20              77        MDI   \n", "48                        NaN     2025-06-20              69     GFMVIF   \n", "49                        NaN     2025-06-20              68       VMPF   \n", "50                        NaN     2025-06-20              80       VDEF   \n", "51                        NaN     2025-06-20              61       PBIF   \n", "52                        NaN     2025-06-20              52       TVPF   \n", "53                        NaN     2025-06-20              86       KDEF   \n", "54                        NaN     2025-06-20              83     RVPF24   \n", "55                        NaN     2025-06-20              82    VCBFAIF   \n", "\n", "     vsd_fee_id  \n", "0    VFMVF1N001  \n", "1      MBVFN001  \n", "2    SSISCAN001  \n", "3   VCBFMGFN001  \n", "4      BVPFN001  \n", "5   VCBFBCFN001  \n", "6    VFMVF4N001  \n", "7   VCBFTBFN001  \n", "8    VCAMBFN001  \n", "9      VLGFN001  \n", "10    BVFEDN001  \n", "11  VCBFFIFN001  \n", "12    VESAFN002  \n", "13    VNDBFN001  \n", "14     MAFFN001  \n", "15     PVBFN001  \n", "16      ENFN001  \n", "17      VFFN003  \n", "18     VIBFN003  \n", "19   MBBONDN001  \n", "20     VEOFN003  \n", "21     ABBFN001  \n", "22   VFMVFBN001  \n", "23     BVBFN001  \n", "24    SSIBFN001  \n", "25     ASBFN001  \n", "26    MAGEFN001  \n", "27       MAFBAL  \n", "28    VNDAFN001  \n", "29   VFMVFCN001  \n", "30     VLBFN001  \n", "31     DFIXN002  \n", "32       MAFEQI  \n", "33     DCAFN002  \n", "34     TBLFN001  \n", "35     BMFFN001  \n", "36   VCAMDFN001  \n", "37     TCGFN001  \n", "38     LHBFN001  \n", "39   VCAMFIN001  \n", "40     MBAMN001  \n", "41    VNDCFN001  \n", "42   HDBONDN001  \n", "43    UVDIFN001  \n", "44    LHCDFN001  \n", "45    UVEEFN001  \n", "46    PHVSFN001  \n", "47          MDI  \n", "48   GFMVIFN001  \n", "49     VMPFN001  \n", "50     VDEFN001  \n", "51     PBIFN001  \n", "52     TVPFN001  \n", "53     KDEFN001  \n", "54   RVPF24N001  \n", "55  VCBFAIFN001  \n", "\n", "[56 rows x 21 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from vnstock.explorer.fmarket.fund import Fund\n", "fund = Fund()\n", "fund.listing()"]}, {"cell_type": "code", "execution_count": 12, "id": "c3b22b7f", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>branch</th>\n", "      <th>buy_price</th>\n", "      <th>sell_price</th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td>Hu<PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Vàng SJC 1L, 10L, 1KG</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>117400000.0</td>\n", "      <td>119400000.0</td>\n", "      <td>2025-06-20</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     name       branch    buy_price   sell_price        date\n", "0   Vàng SJC 1L, 10L, 1K<PERSON>  <PERSON><PERSON>  117400000.0  119400000.0  2025-06-20\n", "1   Vàng SJC 1L, 10L, 1KG     Miền Bắc  117400000.0  119400000.0  2025-06-20\n", "2   Vàng SJC 1L, 10L, 1KG      Hạ Long  117400000.0  119400000.0  2025-06-20\n", "3   Vàng SJC 1L, 10L, 1KG    Hải Phòng  117400000.0  119400000.0  2025-06-20\n", "4   Vàng SJC 1L, 10L, 1KG   Miền Trung  117400000.0  119400000.0  2025-06-20\n", "5   Vàng SJC 1L, 10L, 1KG          Huế  117400000.0  119400000.0  2025-06-20\n", "6   Vàng SJC 1L, 10L, 1KG   Quảng Ngãi  117400000.0  119400000.0  2025-06-20\n", "7   Vàng SJC 1L, 10L, 1KG    Nha Trang  117400000.0  119400000.0  2025-06-20\n", "8   Vàng SJC 1L, 10L, 1KG     B<PERSON>ên Hòa  117400000.0  119400000.0  2025-06-20\n", "9   Vàng SJC 1L, 10L, 1KG     <PERSON>ền Tây  117400000.0  119400000.0  2025-06-20\n", "10  Vàng SJC 1L, 10L, 1KG     Bạc Liêu  117400000.0  119400000.0  2025-06-20\n", "11  Vàng SJC 1L, 10L, 1KG       Cà Mau  117400000.0  119400000.0  2025-06-20"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from vnstock.explorer.misc import *\n", "\n", "# Tỷ giá ngoại tệ VCB\n", "vcb_exchange_rate(date='2024-03-21')\n", "\n", "# <PERSON><PERSON><PERSON> vàng SJC\n", "sjc_gold_price()"]}, {"cell_type": "code", "execution_count": 13, "id": "fd0ca161", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Analyzing performance from 2023-05-26 to 2025-06-20...\n"]}], "source": ["end_date = datetime.now()\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=int(3 * 252))\n", "end_date_str = end_date.strftime('%Y-%m-%d')\n", "start_date_str = start_date.strftime('%Y-%m-%d')\n", "print(f\"\\nAnalyzing performance from {start_date_str} to {end_date_str}...\")"]}, {"cell_type": "code", "execution_count": 20, "id": "b4a50295", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["vnstock API classes initialized with default symbol 'FPT' and source 'VCI'.\n"]}], "source": ["default_symbol = 'FPT'\n", "default_source = 'VCI' # 'VCI', 'TCBS', or 'MSN' as per vnstock traceback\n", "\n", "listing_api = Listing()\n", "quote_api = Quote(symbol=default_symbol, source=default_source)\n", "company_api = Company(symbol=default_symbol, source=default_source)\n", "finance_api = Finance(symbol=default_symbol, source=default_source)\n", "trading_api = Trading(symbol=default_symbol, source=default_source)\n", "screener_api = Screener() # Screener usually operates on the whole market, so no initial symbol needed.\n", "\n", "print(f\"vnstock API classes initialized with default symbol '{default_symbol}' and source '{default_source}'.\")"]}, {"cell_type": "code", "execution_count": 22, "id": "7315350f", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ticker', 'exchange', 'industry', 'market_cap', 'roe', 'stock_rating', 'business_operation', 'business_model', 'financial_health', 'alpha', 'beta', 'uptrend', 'active_buy_pct', 'strong_buy_pct', 'high_vol_match', 'forecast_vol_ratio', 'pe', 'pb', 'ev_ebitda', 'dividend_yield', 'price_vs_sma5', 'price_vs_sma20', 'revenue_growth_1y', 'revenue_growth_5y', 'eps_growth_1y', 'eps_growth_5y', 'gross_margin', 'net_margin', 'doe', 'avg_trading_value_5d', 'avg_trading_value_10d', 'avg_trading_value_20d', 'relative_strength_3d', 'rel_strength_1m', 'rel_strength_3m', 'rel_strength_1y', 'total_trading_value', 'foreign_transaction', 'price_near_realtime', 'rsi14', 'foreign_vol_pct', 'tc_rs', 'tcbs_recommend', 'tcbs_buy_sell_signal', 'foreign_buysell_20s', 'num_increase_continuous_day', 'num_decrease_continuous_day', 'eps', 'macd_histogram', 'vol_vs_sma5', 'vol_vs_sma10', 'vol_vs_sma20', 'vol_vs_sma50', 'price_vs_sma10', 'price_vs_sma50', 'price_break_out52_week', 'price_wash_out52_week', 'sar_vs_macd_hist', 'bolling_band_signal', 'dmi_signal', 'rsi14_status', 'price_growth_1w', 'price_growth_1m', 'breakout', 'prev_1d_growth_pct', 'prev_1m_growth_pct', 'prev_1y_growth_pct', 'prev_5y_growth_pct', 'has_financial_report', 'free_transfer_rate', 'net_cash_per_market_cap', 'net_cash_per_total_assets', 'profit_last_4q', 'last_quarter_revenue_growth', 'second_quarter_revenue_growth', 'last_quarter_profit_growth', 'second_quarter_profit_growth', 'pct_1y_from_peak', 'pct_away_from_hist_peak', 'pct_1y_from_bottom', 'pct_off_hist_bottom', 'price_vs_sma100', 'heating_up', 'price_growth1_day', 'vsma5', 'vsma10', 'vsma20', 'vsma50', 'corporate_percentage', 'ev', 'quarter_revenue_growth', 'quarter_income_growth', 'peg_forward', 'peg_trailing', 'quarterly_income', 'quarterly_revenue', 'ps', 'roa', 'npl', 'nim', 'price_vs_sma200', 'eps_ttm_growth1_year', 'eps_ttm_growth5_year', 'equity_mi', 'eps_recently', 'percent_price_vs_ma200', 'percent_price_vs_ma20', 'percent_price_vs_ma50', 'percent_price_vs_ma100']\n", "\n", "First 5 symbols fetched (using column 'ticker'):\n", "['A32', 'AAA', 'AAH', 'AAM', 'AAS']\n", "Total symbols fetched: 1635\n"]}], "source": ["all_stocks_df = screener_api.stock(params={\"exchangeName\": \"HOSE,HNX,UPCOM\"}, limit=1700)\n", "\n", "print(all_stocks_df.columns.tolist())\n", "\n", "SYMBOL_COLUMN_NAME = 'ticker'\n", "\n", "try:\n", "    # Try to use the identified symbol column name\n", "    symbols = all_stocks_df[SYMBOL_COLUMN_NAME].tolist()\n", "    print(f\"\\nFirst 5 symbols fetched (using column '{SYMBOL_COLUMN_NAME}'):\")\n", "    print(all_stocks_df[SYMBOL_COLUMN_NAME].head().tolist())\n", "    print(f\"Total symbols fetched: {len(symbols)}\")\n", "\n", "    # Store symbols for potential (but not used in subsequent cells due to constraint) iteration\n", "    all_symbols = symbols\n", "\n", "except KeyError:\n", "    print(f\"\\nError: Column '{SYMBOL_COLUMN_NAME}' not found in the DataFrame.\")\n", "    print(\"Please inspect the printed column names above and update SYMBOL_COLUMN_NAME.\")\n", "    print(\"Example: If the columns are ['Code', 'Name', 'Industry'], you should set SYMBOL_COLUMN_NAME = 'Code'\")\n", "    all_symbols = [] # Set to empty to prevent further errors"]}, {"cell_type": "code", "execution_count": 27, "id": "9d03352b", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Displaying 'ticker' and 'ev_ebitda' columns:\n", "ticker  ev_ebitda\n", "   A32        NaN\n", "   AAA        5.3\n", "   AAH        9.2\n", "   AAM        2.7\n", "   AAS       19.3\n", "   AAT       21.7\n", "   AAV     -294.3\n", "   ABA        NaN\n", "   ABB        NaN\n", "   ABC      -41.9\n", "\n", "--- Statistics for ev_ebitda ---\n", "count    1102.000000\n", "mean        9.915426\n", "std       297.787499\n", "min     -3020.000000\n", "25%         4.400000\n", "50%         9.100000\n", "75%        17.400000\n", "max      6539.800000\n", "Name: ev_ebitda, dtype: float64\n", "\n", "Number of companies with missing ev_ebitda data: 533\n"]}], "source": ["all_stocks_df = screener_api.stock(params={\"exchangeName\": \"HOSE,HNX,UPCOM\"}, limit=1700)\n", "\n", "# Assuming 'ticker' is the symbol column name based on previous interactions\n", "SYMBOL_COLUMN_NAME = 'ticker' \n", "\n", "print(f\"\\nDisplaying '{SYMBOL_COLUMN_NAME}' and 'ev_ebitda' columns:\")\n", "\n", "# Check if 'ev_ebitda' column exists\n", "if 'ev_ebitda' in all_stocks_df.columns:\n", "    # Convert 'ev_ebitda' to numeric, coercing errors to NaN\n", "    all_stocks_df['ev_ebitda'] = pd.to_numeric(all_stocks_df['ev_ebitda'], errors='coerce')\n", "\n", "    # Display the first few rows of the 'ticker' and 'ev_ebitda' columns\n", "    print(all_stocks_df[[SYMBOL_COLUMN_NAME, 'ev_ebitda']].head(10).to_string(index=False))\n", "\n", "    # Optional: Display some statistics about the ev_ebitda column\n", "    print(\"\\n--- Statistics for ev_ebitda ---\")\n", "    print(all_stocks_df['ev_ebitda'].describe())\n", "\n", "    # Optional: Count how many companies have missing ev_ebitda data\n", "    missing_ev_ebitda = all_stocks_df['ev_ebitda'].isnull().sum()\n", "    print(f\"\\nNumber of companies with missing ev_ebitda data: {missing_ev_ebitda}\")\n", "else:\n", "    print(\"\\n'ev_ebitda' column not found in the DataFrame. Available columns are:\")\n", "    print(all_stocks_df.columns.tolist())"]}, {"cell_type": "code", "execution_count": 29, "id": "d3ca46d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 10 Companies with the lowest 'stock_rating':\n", "ticker  stock_rating_numeric\n", "   DHM                   1.6\n", "   DPC                   1.6\n", "   DNT                   1.6\n", "   HNG                   1.6\n", "   MCG                   1.6\n", "   GTD                   1.7\n", "   LIC                   1.7\n", "   MZG                   1.8\n", "   DSE                   1.8\n", "   DTA                   1.8\n"]}], "source": ["if 'stock_rating' in all_stocks_df.columns:\n", "    # Convert 'stock_rating' to numeric, coercing any non-numeric values to NaN\n", "    # Create a temporary column for numeric rating to avoid modifying the original 'stock_rating'\n", "    df_temp = all_stocks_df.copy()\n", "    df_temp['stock_rating_numeric'] = pd.to_numeric(df_temp['stock_rating'], errors='coerce')\n", "\n", "    # Drop rows where 'stock_rating_numeric' is NaN as they don't have a valid rating\n", "    df_filtered_by_ratings = df_temp.dropna(subset=['stock_rating_numeric'])\n", "\n", "    if not df_filtered_by_ratings.empty:\n", "        # Sort the DataFrame by the numeric stock rating in ascending order (lowest first)\n", "        sorted_companies = df_filtered_by_ratings.sort_values(\n", "            by='stock_rating_numeric', ascending=True\n", "        )\n", "\n", "        # Get the top 10 companies with the lowest ratings\n", "        top_10_lowest_rating = sorted_companies.head(10)\n", "\n", "        print(f\"\\nTop 10 Companies with the lowest 'stock_rating':\")\n", "        # Display the symbol and their numeric stock rating\n", "        print(top_10_lowest_rating[[SYMBOL_COLUMN_NAME, 'stock_rating_numeric']].to_string(index=False))\n", "    else:\n", "        print(\"\\nNo valid 'stock_rating' data found after cleaning to determine the lowest.\")\n", "else:\n", "    print(\"\\n'stock_rating' column not found in the DataFrame. Cannot scan for lowest ratings.\")\n", "    print(\"Available columns are:\", all_stocks_df.columns.tolist())"]}, {"cell_type": "code", "execution_count": 30, "id": "d759c363", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 10 Companies with the lowest 'financial_health':\n", "ticker  financial_health_numeric\n", "   MCG                       1.2\n", "   BTN                       1.2\n", "   TIN                       1.2\n", "   HNG                       1.2\n", "   TIS                       1.2\n", "   VPB                       1.2\n", "   LNC                       1.4\n", "   DTC                       1.4\n", "   NDT                       1.4\n", "   RDP                       1.4\n"]}], "source": ["all_stocks_df['financial_health_numeric'] = pd.to_numeric(all_stocks_df['financial_health'], errors='coerce')\n", "\n", "# Drop rows with <PERSON><PERSON> in the numeric financial health column and sort to get the top 10 lowest\n", "top_10_lowest_health = all_stocks_df.dropna(subset=['financial_health_numeric']).sort_values(\n", "    by='financial_health_numeric', ascending=True\n", ").head(10)\n", "\n", "# Display the result\n", "print(f\"\\nTop 10 Companies with the lowest 'financial_health':\")\n", "print(top_10_lowest_health[[SYMBOL_COLUMN_NAME, 'financial_health_numeric']].to_string(index=False))"]}, {"cell_type": "code", "execution_count": 40, "id": "3c6882d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 10 Companies with the highest 'financial_health':\n", "ticker  financial_health_numeric\n", "   PTC                       5.0\n", "   STS                       5.0\n", "   HMH                       5.0\n", "   STG                       5.0\n", "   BHN                       5.0\n", "   DTT                       5.0\n", "   SDC                       5.0\n", "   PNP                       5.0\n", "   DXL                       5.0\n", "   NDF                       5.0\n"]}, {"data": {"text/markdown": ["\n", "## 👋 Chào mừng bạn đến với <PERSON>!\n", "\n", "Cảm ơn bạn đã sử dụng package phân tích chứng khoán #1 tại Việt Nam\n", "\n", "* Tài liệu: [Sổ tay hướng dẫn](https://vnstocks.com/docs/category/s%E1%BB%95-tay-h%C6%B0%E1%BB%9Bng-d%E1%BA%ABn)\n", "* Cộng đồng: [Nhóm Facebook](https://www.facebook.com/groups/vnstock.official)\n", "\n", "<PERSON><PERSON><PERSON><PERSON> phá các tính năng mới nhất và tham gia cộng đồng để nhận hỗ trợ.\n", "                "], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["all_stocks_df['financial_health_numeric'] = pd.to_numeric(all_stocks_df['financial_health'], errors='coerce')\n", "\n", "# Drop rows with <PERSON><PERSON> in the numeric financial health column and sort to get the top 10 highest\n", "top_10_highest_health = all_stocks_df.dropna(subset=['financial_health_numeric']).sort_values(\n", "    by='financial_health_numeric', ascending=False\n", ").head(10)\n", "\n", "# Display the result\n", "print(f\"\\nTop 10 Companies with the highest 'financial_health':\")\n", "# Corrected variable name from 'top_10_lowest_health' to 'top_10_highest_health'\n", "print(top_10_highest_health[[SYMBOL_COLUMN_NAME, 'financial_health_numeric']].to_string(index=False))"]}, {"cell_type": "code", "execution_count": 36, "id": "dd5a7481", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 20 Companies with the lowest average 'stock_rating' and 'financial_health':\n", "ticker  stock_rating_numeric  financial_health_numeric  average_rating\n", "   HNG                   1.6                       1.2            1.40\n", "   MCG                   1.6                       1.2            1.40\n", "   DPC                   1.6                       1.4            1.50\n", "   TIS                   1.9                       1.2            1.55\n", "   GTD                   1.7                       1.6            1.65\n", "   BTS                   1.9                       1.4            1.65\n", "   LIC                   1.7                       1.8            1.75\n", "   TTE                   2.1                       1.4            1.75\n", "   DTL                   1.8                       1.8            1.80\n", "   ABB                   2.3                       1.4            1.85\n", "   SMC                   2.1                       1.6            1.85\n", "   VPB                   2.5                       1.2            1.85\n", "   PIT                   1.9                       1.8            1.85\n", "   UDC                   2.1                       1.6            1.85\n", "   MZG                   1.8                       2.0            1.90\n", "   DTA                   1.8                       2.0            1.90\n", "   RIC                   2.0                       1.8            1.90\n", "   VIB                   2.4                       1.4            1.90\n", "   DNM                   2.2                       1.6            1.90\n", "   NBB                   1.9                       2.0            1.95\n"]}], "source": ["df_scores = all_stocks_df.copy()\n", "\n", "# Convert 'stock_rating' and 'financial_health' to numeric, coercing errors to NaN\n", "df_scores['stock_rating_numeric'] = pd.to_numeric(df_scores['stock_rating'], errors='coerce')\n", "df_scores['financial_health_numeric'] = pd.to_numeric(df_scores['financial_health'], errors='coerce')\n", "\n", "# Drop rows where either rating is NaN, so only companies with both scores are averaged\n", "# This ensures a meaningful average.\n", "df_scores_clean = df_scores.dropna(subset=['stock_rating_numeric', 'financial_health_numeric'])\n", "\n", "# Calculate the average score\n", "df_scores_clean['average_rating'] = (df_scores_clean['stock_rating_numeric'] + df_scores_clean['financial_health_numeric']) / 2\n", "\n", "# Find the top 20 companies with the lowest average score by sorting and taking the head(20)\n", "top_20_lowest_average_score = df_scores_clean.sort_values(by='average_rating', ascending=True).head(20)\n", "\n", "# Display the result\n", "print(f\"\\nTop 20 Companies with the lowest average 'stock_rating' and 'financial_health':\")\n", "print(top_20_lowest_average_score[[\n", "    SYMBOL_COLUMN_NAME,\n", "    'stock_rating_numeric',\n", "    'financial_health_numeric',\n", "    'average_rating'\n", "]].to_string(index=False))"]}, {"cell_type": "code", "execution_count": 41, "id": "c0a35d95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scanning for specified metrics for the top 30 companies with lowest average ratings (with all non-NaN figures):\n", "ticker  roe     pe  business_model  eps_growth_1y  eps_growth_5y  foreign_vol_pct  rel_strength_1y\n", "   ABB  5.1   12.1             3.0          29.12         -17.27            16.41             54.0\n", "   BVB  5.3   25.5             3.1         450.23          17.60             0.09             76.0\n", "   NBB  0.1 2333.0             3.0         -59.90         -70.58             0.46             38.0\n", "   PBC  2.2   28.1             3.0         -44.92          14.00             0.00             27.0\n", "   SHN  0.7   68.2             3.0         164.78         -34.09             0.01             39.0\n", "   VIB 17.4    7.6             3.0         -15.87           2.83             4.99             52.0\n", "   VPB 11.3    9.1             3.5          58.20           1.37            24.70             51.0\n"]}], "source": ["df_scores = all_stocks_df.copy()\n", "\n", "df_scores['stock_rating_numeric'] = pd.to_numeric(df_scores['stock_rating'], errors='coerce')\n", "df_scores['financial_health_numeric'] = pd.to_numeric(df_scores['financial_health'], errors='coerce')\n", "\n", "df_scores_clean = df_scores.dropna(subset=['stock_rating_numeric', 'financial_health_numeric'])\n", "\n", "df_scores_clean['average_rating'] = (df_scores_clean['stock_rating_numeric'] + df_scores_clean['financial_health_numeric']) / 2\n", "\n", "# Find the top 30 companies with the lowest average score by sorting and taking the head(30)\n", "top_30_lowest_average_score = df_scores_clean.sort_values(by='average_rating', ascending=True).head(30)\n", "\n", "# Get the list of tickers from the top 30 companies\n", "top_30_tickers = top_30_lowest_average_score[SYMBOL_COLUMN_NAME].tolist()\n", "\n", "# --- Scan for the requested metrics for these companies ---\n", "print(f\"Scanning for specified metrics for the top 30 companies with lowest average ratings (with all non-NaN figures):\")\n", "\n", "# Define the list of columns to display, excluding 'price_vs_sma50'\n", "metrics_to_display = [\n", "    SYMBOL_COLUMN_NAME,\n", "    'roe',\n", "    'pe',\n", "    'business_model',\n", "    'eps_growth_1y',\n", "    'eps_growth_5y',\n", "    'foreign_vol_pct',\n", "    'rel_strength_1y' # 'price_vs_sma50' is removed\n", "]\n", "\n", "# Filter the original DataFrame for only these top 30 tickers\n", "df_filtered_companies = all_stocks_df[all_stocks_df[SYMBOL_COLUMN_NAME].isin(top_30_tickers)].copy()\n", "\n", "# Convert relevant columns to numeric, coercing errors\n", "for col in ['roe', 'pe', 'eps_growth_1y', 'eps_growth_5y', 'foreign_vol_pct', 'rel_strength_1y']:\n", "    # Only attempt conversion if the column exists in the filtered DataFrame\n", "    if col in df_filtered_companies.columns:\n", "        df_filtered_companies[col] = pd.to_numeric(df_filtered_companies[col], errors='coerce')\n", "\n", "cols_for_dropna_check = [col for col in metrics_to_display if col != SYMBOL_COLUMN_NAME and col in df_filtered_companies.columns]\n", "\n", "# Filter out rows with any NaN values in the specified metrics\n", "df_final_display = df_filtered_companies.dropna(subset=cols_for_dropna_check)\n", "\n", "actual_metrics_to_display = [col for col in metrics_to_display if col in df_final_display.columns]\n", "\n", "print(df_final_display[actual_metrics_to_display].to_string(index=False))"]}, {"cell_type": "code", "execution_count": 44, "id": "aae9bd21", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'stock_historical_data' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[44], line 5\u001b[0m\n\u001b[0;32m      3\u001b[0m start_date \u001b[38;5;241m=\u001b[39m (datetime\u001b[38;5;241m.\u001b[39mnow() \u001b[38;5;241m-\u001b[39m timedelta(days\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m5\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m365\u001b[39m))\u001b[38;5;241m.\u001b[39mstrftime(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mY-\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mm-\u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;66;03m# Approximately 5 years ago\u001b[39;00m\n\u001b[0;32m      4\u001b[0m initial_investment \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m \u001b[38;5;66;03m# USD or equivalent VND\u001b[39;00m\n\u001b[1;32m----> 5\u001b[0m df \u001b[38;5;241m=\u001b[39m stock_historical_data(symbol\u001b[38;5;241m=\u001b[39mticker, start_date\u001b[38;5;241m=\u001b[39mstart_date, end_date\u001b[38;5;241m=\u001b[39mend_date)\n\u001b[0;32m      7\u001b[0m \u001b[38;5;66;03m# --- Prepare data for plotting ---\u001b[39;00m\n\u001b[0;32m      8\u001b[0m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDate\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mto_datetime(df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDate\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "\u001b[1;31mNameError\u001b[0m: name 'stock_historical_data' is not defined"]}], "source": ["ticker = \"NBB\"\n", "end_date = datetime.now().strftime('%Y-%m-%d')\n", "start_date = (datetime.now() - timed<PERSON>ta(days=5*365)).strftime('%Y-%m-%d') # Approximately 5 years ago\n", "initial_investment = 1 # USD or equivalent VND\n", "df = stock_historical_data(symbol=ticker, start_date=start_date, end_date=end_date)\n", "\n", "# --- Prepare data for plotting ---\n", "df['Date'] = pd.to_datetime(df['Date'])\n", "df.set_index('Date', inplace=True)\n", "df = df.sort_index() # Ensure chronological order\n", "\n", "# Calculate daily returns\n", "df['Daily_Return'] = df['Close'].pct_change()\n", "df['Daily_Return'].fillna(0, inplace=True) # Handle first NaN or any missing returns\n", "\n", "# Calculate cumulative growth of $1 investment\n", "df['Cumulative_Value'] = (1 + df['Daily_Return']).cumprod()\n", "\n", "# --- Plotting ---\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(df.index, df['Cumulative_Value'], label=f'{ticker} Cumulative Value ($1 Start)')\n", "plt.title(f'Backtest Performance of {ticker}')\n", "plt.xlabel('Date')\n", "plt.ylabel('Cumulative Value (Starting at $1)')\n", "plt.grid(True)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
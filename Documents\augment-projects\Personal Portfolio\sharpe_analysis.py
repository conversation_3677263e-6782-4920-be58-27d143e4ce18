#!/usr/bin/env python3
"""
Vietnamese Stock Sharpe Ratio Analysis

This script analyzes Vietnamese stocks to identify the top 15 tickers with the highest Sharpe ratio.
It uses vnstock to fetch historical price data and calculates 5-year volatility and Sharpe ratios.
"""

import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Import vnstock
try:
    from vnstock import Vnstock, Screener
except ImportError:
    print("❌ Error importing vnstock. Please install it with: pip install vnstock")
    exit(1)

def fetch_stock_data(ticker, start_date_str, end_date_str, retries=2):
    """
    Fetch historical stock data using vnstock with retry mechanism
    """
    for attempt in range(retries):
        try:
            stock_instance = Vnstock().stock(symbol=ticker, source='VCI')
            df = stock_instance.quote.history(
                start=start_date_str,
                end=end_date_str,
                interval='1D'
            )

            if df is not None and not df.empty:
                return df

        except Exception:
            if attempt < retries - 1:
                time.sleep(0.5)

    return None

def calculate_sharpe_ratio(df, risk_free_rate=0.04):
    """
    Calculate Sharpe ratio from historical price data
    """
    try:
        df['close'] = pd.to_numeric(df['close'], errors='coerce')
        df['daily_return'] = df['close'].pct_change()
        returns = df['daily_return'].dropna()

        if len(returns) < 1000:
            return None, None, None

        annual_return = returns.mean() * 252
        volatility = returns.std() * np.sqrt(252)

        if np.isnan(annual_return) or np.isnan(volatility) or volatility == 0:
            return None, None, None

        sharpe_ratio = (annual_return - risk_free_rate) / volatility
        return annual_return, volatility, sharpe_ratio

    except Exception:
        return None, None, None

def main():
    print("Vietnamese Stock Sharpe Ratio Analysis")

    end_date = datetime.now()
    start_date = end_date - timedelta(days=5*365)
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')

    print(f"Period: {start_date_str} to {end_date_str}")

    # Get all Vietnamese stocks using Screener
    try:
        screener = Screener()
        # Get all stocks from HOSE and HNX exchanges
        all_stocks = []

        # Try to get stocks from different exchanges
        for exchange in ['HOSE', 'HNX']:
            try:
                stocks = screener.symbols_by_exchange(exchange)
                if stocks is not None and not stocks.empty:
                    all_stocks.extend(stocks['symbol'].tolist())
            except Exception:
                continue

        if not all_stocks:
            # Fallback: try getting all symbols
            try:
                all_symbols = screener.all_symbols()
                if all_symbols is not None and not all_symbols.empty:
                    all_stocks = all_symbols['symbol'].tolist()
            except Exception:
                pass

        tickers = list(set(all_stocks))  # Remove duplicates
        print(f"Found {len(tickers)} stocks to analyze")

    except Exception:
        tickers = []
    
    # Store results
    results = []
    
    # Process each ticker
    for i, ticker in enumerate(tickers):
        if i % 10 == 0:
            print(f"Processing {i+1}/{len(tickers)}")

        # Fetch historical data
        df = fetch_stock_data(ticker, start_date_str, end_date_str)

        if df is None or df.empty:
            continue

        # Calculate Sharpe ratio
        annual_return, volatility, sharpe_ratio = calculate_sharpe_ratio(df)

        if annual_return is None or volatility is None or sharpe_ratio is None:
            continue

        # Add to results
        results.append({
            'ticker': ticker,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'data_points': len(df)
        })

        # Add delay to avoid rate limiting
        time.sleep(0.5)
    
    # Create DataFrame from results
    if results:
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values('sharpe_ratio', ascending=False)

        # Display top 15 results
        print("\nTOP 15 VIETNAMESE STOCKS BY SHARPE RATIO")
        print("=" * 60)

        top_15 = results_df.head(15).copy()
        top_15['annual_return'] = (top_15['annual_return'] * 100).round(3)
        top_15['volatility'] = (top_15['volatility'] * 100).round(3)
        top_15['sharpe_ratio'] = top_15['sharpe_ratio'].round(3)

        print("\n{:<6} {:<8} {:<12} {:<14} {:<12}".format(
            "Rank", "Ticker", "Return (%)", "Volatility (%)", "Sharpe Ratio"))
        print("-" * 60)

        for i, (_, row) in enumerate(top_15.iterrows()):
            print("{:<6} {:<8} {:<12} {:<14} {:<12}".format(
                i+1,
                row['ticker'],
                row['annual_return'],
                row['volatility'],
                row['sharpe_ratio']
            ))

        print(f"\nAnalyzed {len(results_df)} stocks successfully")
        print(f"Period: {start_date_str} to {end_date_str}")

    else:
        print("No valid results found")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nAnalysis interrupted")
    except Exception as e:
        print(f"Error: {e}")

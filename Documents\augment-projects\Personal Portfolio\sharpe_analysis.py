#!/usr/bin/env python3
"""
Vietnamese Stock Sharpe Ratio Analysis

This script analyzes Vietnamese stocks to identify the top 15 tickers with the highest Sharpe ratio.
It uses vnstock to fetch historical price data and calculates 5-year volatility and Sharpe ratios.
"""

import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback

# Import vnstock
try:
    from vnstock import Vnstock, Screener
    print("✓ vnstock imported successfully")
except ImportError:
    print("❌ Error importing vnstock. Please install it with: pip install vnstock")
    exit(1)

def fetch_stock_data(ticker, start_date_str, end_date_str, retries=3):
    """
    Fetch historical stock data using vnstock with retry mechanism
    """
    for attempt in range(retries):
        try:
            # Create stock instance using the correct API pattern
            stock_instance = Vnstock().stock(symbol=ticker, source='VCI')
            
            # Fetch historical data
            df = stock_instance.quote.history(
                start=start_date_str, 
                end=end_date_str, 
                interval='1D'
            )
            
            if df is not None and not df.empty:
                return df
            
        except Exception as e:
            if attempt < retries - 1:
                print(f"  Retry {attempt+1}/{retries} for {ticker}: {str(e)[:50]}...")
                time.sleep(1)  # Wait before retrying
            else:
                print(f"  ❌ Failed to fetch data for {ticker}: {str(e)[:50]}...")
    
    return None

def calculate_sharpe_ratio(df, risk_free_rate=0.04):
    """
    Calculate Sharpe ratio from historical price data
    """
    try:
        # Ensure 'close' column is numeric
        df['close'] = pd.to_numeric(df['close'], errors='coerce')
        
        # Calculate daily returns
        df['daily_return'] = df['close'].pct_change()
        
        # Drop NaN values
        returns = df['daily_return'].dropna()
        
        if len(returns) < 1000:  # Require at least 1000 days of data for reliable calculation
            return None, None, None
        
        # Calculate annualized metrics
        annual_return = returns.mean() * 252
        volatility = returns.std() * np.sqrt(252)
        
        # Check for valid values
        if np.isnan(annual_return) or np.isnan(volatility) or volatility == 0:
            return None, None, None
        
        # Calculate Sharpe ratio
        sharpe_ratio = (annual_return - risk_free_rate) / volatility
        
        return annual_return, volatility, sharpe_ratio
        
    except Exception as e:
        print(f"  ❌ Error calculating Sharpe ratio: {str(e)[:50]}...")
        return None, None, None

def main():
    print("\n📊 VIETNAMESE STOCK SHARPE RATIO ANALYSIS 📊")
    print("=" * 60)
    
    # Set date range for 5-year analysis
    end_date = datetime.now()
    start_date = end_date - timedelta(days=5*365)
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    print(f"Analyzing 5-year period: {start_date_str} to {end_date_str}")
    
    # Get list of Vietnamese stocks
    try:
        # Use a comprehensive list of major Vietnamese stocks for testing
        # In a real scenario, you would use Screener to get all stocks
        major_vn_stocks = [
            'VIC', 'VHM', 'VCB', 'BID', 'CTG', 'VNM', 'GAS', 'MSN', 'PLX', 'TCB',
            'HPG', 'VRE', 'SAB', 'MWG', 'FPT', 'POW', 'VJC', 'ACB', 'TPB', 'SSI',
            'VPB', 'HDB', 'MBB', 'VIB', 'STB', 'EIB', 'LPB', 'OCB', 'SHB', 'BVB',
            'VCI', 'HCM', 'AGR', 'BSI', 'CTS', 'TVS', 'VDS', 'VND', 'SHS', 'MBS',
            'PVD', 'PVS', 'PVT', 'DPM', 'DCM', 'GEX', 'REE', 'PC1', 'NT2', 'PNJ'
        ]
        
        # Limit to 50 stocks for testing purposes
        tickers = major_vn_stocks[:50]
        print(f"Analyzing {len(tickers)} Vietnamese stocks...")
        
    except Exception as e:
        print(f"❌ Error getting stock list: {e}")
        tickers = []
    
    # Store results
    results = []
    
    # Process each ticker
    for i, ticker in enumerate(tickers):
        # Print progress every 10 stocks
        if i % 10 == 0:
            print(f"\nProcessing stocks {i+1}-{min(i+10, len(tickers))} of {len(tickers)}...")
        
        print(f"  Analyzing {ticker}...", end="", flush=True)
        
        # Fetch historical data
        df = fetch_stock_data(ticker, start_date_str, end_date_str)
        
        if df is None or df.empty:
            print(" Skipped (no data)")
            continue
        
        # Calculate Sharpe ratio
        annual_return, volatility, sharpe_ratio = calculate_sharpe_ratio(df)
        
        if annual_return is None or volatility is None or sharpe_ratio is None:
            print(" Skipped (insufficient data or calculation error)")
            continue
        
        # Add to results
        results.append({
            'ticker': ticker,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'data_points': len(df)
        })
        
        print(f" ✓ Done (Sharpe: {sharpe_ratio:.3f}, Return: {annual_return*100:.2f}%, Vol: {volatility*100:.2f}%)")
        
        # Add delay to avoid rate limiting
        time.sleep(0.5)
    
    # Create DataFrame from results
    if results:
        results_df = pd.DataFrame(results)
        
        # Sort by Sharpe ratio (descending)
        results_df = results_df.sort_values('sharpe_ratio', ascending=False)
        
        # Display top 15 results
        print("\n🏆 TOP 15 VIETNAMESE STOCKS BY SHARPE RATIO 🏆")
        print("=" * 70)
        
        # Format results table
        top_15 = results_df.head(15).copy()
        top_15['annual_return'] = (top_15['annual_return'] * 100).round(3)
        top_15['volatility'] = (top_15['volatility'] * 100).round(3)
        top_15['sharpe_ratio'] = top_15['sharpe_ratio'].round(3)
        
        # Print formatted table
        print("\n{:<6} {:<8} {:<14} {:<16} {:<14} {:<12}".format(
            "Rank", "Ticker", "Return (%)", "Volatility (%)", "Sharpe Ratio", "Data Points"))
        print("-" * 70)
        
        for i, (_, row) in enumerate(top_15.iterrows()):
            print("{:<6} {:<8} {:<14} {:<16} {:<14} {:<12}".format(
                i+1,
                row['ticker'],
                row['annual_return'],
                row['volatility'],
                row['sharpe_ratio'],
                results_df.iloc[i]['data_points']
            ))
        
        print("\n📈 ANALYSIS SUMMARY:")
        print("-" * 30)
        print(f"Total stocks successfully analyzed: {len(results_df)}")
        print(f"Analysis period: {start_date_str} to {end_date_str} (5 years)")
        print("Risk-free rate used: 4%")
        print("Minimum data requirement: 1000 trading days")
        
        # Show some statistics
        avg_sharpe = results_df['sharpe_ratio'].mean()
        max_sharpe = results_df['sharpe_ratio'].max()
        min_sharpe = results_df['sharpe_ratio'].min()
        
        print(f"\nSharpe Ratio Statistics:")
        print(f"  Average: {avg_sharpe:.3f}")
        print(f"  Maximum: {max_sharpe:.3f} ({results_df.iloc[0]['ticker']})")
        print(f"  Minimum: {min_sharpe:.3f} ({results_df.iloc[-1]['ticker']})")
        
    else:
        print("\n❌ No valid results found. Please check your internet connection and try again.")
        print("Common issues:")
        print("- Network connectivity problems")
        print("- vnstock API rate limiting")
        print("- Invalid stock symbols")
        print("- Insufficient historical data")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Analysis interrupted by user")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        print("\nDetailed error information:")
        traceback.print_exc()
